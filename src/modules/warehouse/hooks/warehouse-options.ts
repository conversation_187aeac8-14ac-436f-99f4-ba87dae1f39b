import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const warehouseOptions = ({ warehouse }: serviceRegistry) =>
	queryOptions({
		queryKey: ["warehouses"],
		queryFn: () => AppRuntime.runPromise(warehouse.getAll()),
	});

export const warehouseOptionsById = (
	{ warehouse }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["warehouses", id],
		queryFn: () => AppRuntime.runPromise(warehouse.getById(id)),
	});
