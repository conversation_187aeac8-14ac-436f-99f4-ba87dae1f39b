import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";

import { warehouseOptions } from "./warehouse-options";

export default function useDeleteWarehouse() {
	const service = useService();
	const { warehouse } = service;
	const queryClient = useQueryClient();
	const queryKey = warehouseOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(warehouse.delete(id)),
		onMutate: async (deletedId) => {
			await queryClient.cancelQueries({ queryKey });
			const previousWarehouses = queryClient.getQueryData(queryKey);

			queryClient.setQueryData(queryKey, (old: any) =>
				create(old, (draft: any) => {
					if (draft) {
						const index = draft.findIndex((w: any) => w.id === deletedId);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}
				}),
			);

			return { previousWarehouses };
		},
		onError: (_err, _deletedId, context) => {
			queryClient.setQueryData(queryKey, context?.previousWarehouses);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
