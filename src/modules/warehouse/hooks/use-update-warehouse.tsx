import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Warehouse, UpdateWarehouse } from "../service/model/warehouse";
import { warehouseOptions } from "./warehouse-options";

export default function useUpdateWarehouse() {
	const service = useService();
	const { warehouse } = service;
	const queryClient = useQueryClient();
	const queryKey = warehouseOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedWarehouse: UpdateWarehouse) =>
			AppRuntime.runPromise(warehouse.update(updatedWarehouse)),
		onMutate: async (updatedWarehouse) => {
			await queryClient.cancelQueries({ queryKey });
			const previousWarehouses = queryClient.getQueryData<Warehouse[]>(queryKey);

			queryClient.setQueryData<Warehouse[]>(queryKey, (old) =>
				create(old, (draft) => {
					if (draft) {
						const index = draft.findIndex((w) => w.id === updatedWarehouse.id);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								...updatedWarehouse,
								updatedAt: new Date().toISOString(),
							};
						}
					}
				}),
			);

			return { previousWarehouses };
		},
		onError: (_err, _updatedWarehouse, context) => {
			queryClient.setQueryData(queryKey, context?.previousWarehouses);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
