import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Warehouse, CreateWarehouse } from "../service/model/warehouse";
import { warehouseOptions } from "./warehouse-options";

export default function useCreateWarehouse() {
	const service = useService();
	const { warehouse } = service;
	const queryClient = useQueryClient();
	const queryKey = warehouseOptions(service).queryKey;

	return useMutation({
		mutationFn: (newWarehouse: CreateWarehouse) =>
			AppRuntime.runPromise(warehouse.create(newWarehouse)),
		onMutate: async (newWarehouse) => {
			await queryClient.cancelQueries({ queryKey });
			const previousWarehouses = queryClient.getQueryData<Warehouse[]>(queryKey);

			queryClient.setQueryData<Warehouse[]>(queryKey, (old) =>
				create(old, (draft) => {
					if (draft) {
						draft.push({
							id: "temp-id",
							...newWarehouse,
							createdAt: new Date().toISOString(),
							updatedAt: new Date().toISOString(),
							deletedAt: null,
						});
					}
				}),
			);

			return { previousWarehouses };
		},
		onError: (_err, _newWarehouse, context) => {
			queryClient.setQueryData(queryKey, context?.previousWarehouses);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
