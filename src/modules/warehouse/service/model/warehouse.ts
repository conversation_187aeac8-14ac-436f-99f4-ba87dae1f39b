import { Schema } from "effect";

export enum WarehouseType {
	Physical = "physical",
	Digital = "digital",
}

export enum WarehouseCategory {
	Provider = "provider",
	Client = "client",
	Lost = "lost",
	Storage = "storage",
	Custom = "custom",
}

export const Warehouse = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.Enums(WarehouseType),
	category: Schema.Enums(WarehouseCategory),
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Warehouse = typeof Warehouse.Type;

export const CreateWarehouse = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	type: Schema.Enums(WarehouseType),
	category: Schema.Enums(WarehouseCategory),
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
});
export type CreateWarehouse = typeof CreateWarehouse.Type;

export const UpdateWarehouse = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.Enums(WarehouseType),
	category: Schema.Enums(WarehouseCategory),
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
});
export type UpdateWarehouse = typeof UpdateWarehouse.Type;
