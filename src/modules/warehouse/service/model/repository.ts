import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { Warehouse, CreateWarehouse, UpdateWarehouse } from "./warehouse";

export class WarehouseRepository extends Effect.Tag("WarehouseRepository")<
	WarehouseRepository,
	{
		readonly getAll: () => Effect.Effect<Warehouse[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Warehouse, AppError>;
		readonly create: (warehouse: CreateWarehouse) => Effect.Effect<string, AppError>;
		readonly update: (warehouse: UpdateWarehouse) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
		readonly validateName: (name: string) => Effect.Effect<void, AppError>;
	}
>() {}
