import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import {
	handleDResponse,
	handleResponse,
} from "src/core/service/repo/api/utils";
import { WarehouseRepository } from "../../model/repository";
import type { CreateWarehouse, UpdateWarehouse } from "../../model/warehouse";
import {
	CreateWarehouseApiFromCreateWarehouse,
	CreateWarehouseApiResponse,
	UpdateWarehouseApiFromUpdateWarehouse,
	WarehouseFromApi,
	WarehouseListFromApi,
} from "./dto";

const baseUrl = "/v1/warehouses";

const makeWarehouseApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(WarehouseListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(WarehouseFromApi))),
		create: (warehouse: CreateWarehouse) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateWarehouseApiFromCreateWarehouse)(
							warehouse,
						),
					),
				})
				.pipe(
					Effect.flatMap(handleDResponse(CreateWarehouseApiResponse)),
					Effect.map((response) => response.id),
				),
		update: (warehouse: UpdateWarehouse) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateWarehouseApiFromUpdateWarehouse)(
							warehouse,
						),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate-code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
		validateName: (name: string) =>
			httpClient
				.get(`${baseUrl}/validate-name/${name}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const warehouseApiRepoLive = Layer.effect(
	WarehouseRepository,
	makeWarehouseApiRepo,
);
