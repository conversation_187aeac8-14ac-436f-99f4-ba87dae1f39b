import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { getErrorResult } from "~/core/utils/effectErrors";
import { cn } from "~/core/utils/classes";
import useDeleteWarehouse from "../../hooks/use-delete-warehouse";
import type { Warehouse } from "../../service/model/warehouse";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	warehouse: Warehouse;
}

export default function DeleteWarehouseModal({ isOpen, setIsOpen, warehouse }: Props) {
	const { mutate: deleteWarehouse, isPending } = useDeleteWarehouse();

	const handleDelete = () => {
		deleteWarehouse(warehouse.id, {
			onSuccess: () => {
				toast.success("Almacén eliminado");
				setIsOpen(false);
			},
			onError: (error) => {
				const { error: err } = getErrorResult(error);
				toast.error(err.message);
			},
		});
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar Almacén</h3>
				<p className="py-4">
					¿Está seguro de que desea eliminar el almacén <strong>"{warehouse.name}"</strong>?
					Esta acción no se puede deshacer.
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? "Eliminando..." : "Eliminar"}
					</button>
				</div>
			</div>
		</div>
	);
}
