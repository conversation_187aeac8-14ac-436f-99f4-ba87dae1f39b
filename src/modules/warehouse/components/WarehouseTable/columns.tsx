import { createColumnHelper } from "@tanstack/react-table";
import {
	AlertTriangle,
	Building,
	CheckCircle,
	Cloud,
	Edit,
	HardDrive,
	Package,
	Settings,
	Shield,
	Trash,
	Truck,
	Users,
	XCircle,
} from "lucide-react";
import { useState } from "react";
import type { Warehouse } from "../../service/model/warehouse";
import DeleteWarehouseModal from "../DeleteWarehouseModal";
import EditWarehouseModal from "../EditWarehouseModal";

const columnHelper = createColumnHelper<Warehouse>();

const getTypeIcon = (type: string) => {
	switch (type) {
		case "physical":
			return <HardDrive size={16} className="text-blue-600" />;
		case "digital":
			return <Cloud size={16} className="text-purple-600" />;
		default:
			return <Building size={16} className="text-gray-600" />;
	}
};

const getTypeLabel = (type: string) => {
	switch (type) {
		case "physical":
			return "Físico";
		case "digital":
			return "Digital";
		default:
			return type;
	}
};

const getCategoryIcon = (category: string) => {
	switch (category) {
		case "provider":
			return <Truck size={16} className="text-green-600" />;
		case "client":
			return <Users size={16} className="text-blue-600" />;
		case "lost":
			return <AlertTriangle size={16} className="text-red-600" />;
		case "storage":
			return <Package size={16} className="text-orange-600" />;
		case "custom":
			return <Settings size={16} className="text-gray-600" />;
		default:
			return <Package size={16} className="text-gray-600" />;
	}
};

const getCategoryLabel = (category: string) => {
	switch (category) {
		case "provider":
			return "Proveedor";
		case "client":
			return "Cliente";
		case "lost":
			return "Perdida";
		case "storage":
			return "Almacenamiento";
		case "custom":
			return "Personalizado";
		default:
			return category;
	}
};

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => {
			const warehouse = info.row.original;
			return (
				<div>
					<div className="font-bold">{info.getValue()}</div>
					{warehouse.description && (
						<div className="text-sm opacity-50">{warehouse.description}</div>
					)}
				</div>
			);
		},
	}),
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => (
			<span className="font-mono text-sm">{info.getValue()}</span>
		),
	}),
	columnHelper.accessor("type", {
		header: "Tipo",
		cell: (info) => {
			const type = info.getValue();
			return (
				<div className="flex items-center gap-2">
					{getTypeIcon(type)}
					<span
						className={`badge ${type === "physical" ? "badge-primary" : "badge-secondary"}`}
					>
						{getTypeLabel(type)}
					</span>
				</div>
			);
		},
	}),
	columnHelper.accessor("category", {
		header: "Categoría",
		cell: (info) => {
			const category = info.getValue();
			return (
				<div className="flex items-center gap-2">
					{getCategoryIcon(category)}
					<span className="text-sm">{getCategoryLabel(category)}</span>
				</div>
			);
		},
	}),
	columnHelper.accessor("isActive", {
		header: "Estado",
		cell: (info) => {
			const isActive = info.getValue();
			return (
				<div className="flex items-center gap-2">
					{isActive ? (
						<CheckCircle size={16} className="text-green-600" />
					) : (
						<XCircle size={16} className="text-red-600" />
					)}
					<span
						className={`badge ${isActive ? "badge-success" : "badge-error"}`}
					>
						{isActive ? "Activo" : "Inactivo"}
					</span>
				</div>
			);
		},
	}),
	columnHelper.accessor("isSystemWarehouse", {
		header: "Sistema",
		cell: (info) => {
			const isSystem = info.getValue();
			return (
				<div className="flex items-center gap-2">
					{isSystem && <Shield size={16} className="text-blue-600" />}
					<span className={`badge ${isSystem ? "badge-info" : "badge-ghost"}`}>
						{isSystem ? "Sistema" : "Normal"}
					</span>
				</div>
			);
		},
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const warehouse = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditWarehouseModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={warehouse.id}
					/>
					<DeleteWarehouseModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						warehouse={warehouse}
					/>
				</div>
			);
		},
	}),
];
