import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { warehouseOptions } from "~/warehouse/hooks/warehouse-options";
import Table from "./table";

export default function WarehouseTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(warehouseOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table warehouses={data} />;
}
