import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { Warehouse } from "../../service/model/warehouse";
import { columns } from "./columns";

interface Props {
	warehouses: Warehouse[];
}

export default function Table({ warehouses }: Props) {
	const table = useReactTable({
		data: warehouses,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});

	return <BasicTable table={table} />;
}
