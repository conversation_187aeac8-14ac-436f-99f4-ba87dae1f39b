import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import TextModal from "src/core/components/TextModal";
import { getErrorResult } from "src/core/utils/effectErrors";
import { warehouseOptionsById } from "../../hooks/warehouse-options";
import EditWarehouseForm from "./EditWarehouseForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditWarehouseModal({ isOpen, setIsOpen, id }: Props) {
	const svc = useService();
	const { data, isError, error, isPending } = useQuery({
		...warehouseOptionsById(svc, id),
		enabled: isOpen,
	});

	useEffect(() => {
		if (error) {
			console.log(error);
		}
	}, [error]);

	if (isPending) return <TextModal text="Cargando..." />;

	if (isError)
		return (
			<TextModal
				text="No se pudo cargar el almacén"
				title={getErrorResult(error).error.code.toString()}
			/>
		);

	return <EditWarehouseForm isOpen={isOpen} setIsOpen={setIsOpen} warehouse={data} />;
}
