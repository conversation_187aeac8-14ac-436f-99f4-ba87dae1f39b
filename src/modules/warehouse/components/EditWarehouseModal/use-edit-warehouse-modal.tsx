import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useUpdateWarehouse from "../../hooks/use-update-warehouse";
import type { Warehouse } from "../../service/model/warehouse";
import { CreateWarehouseSchema } from "../CreateWarehouseModal/schema";

export interface EditWarehouseModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	warehouse: Warehouse;
}

export default function useEditWarehouseModal({
	setIsOpen,
	warehouse,
}: EditWarehouseModalProps) {
	const { mutate } = useUpdateWarehouse();

	const form = useAppForm({
		defaultValues: {
			name: warehouse.name,
			code: warehouse.code,
			type: warehouse.type,
			category: warehouse.category,
			description: warehouse.description,
			address: warehouse.address,
			isActive: warehouse.isActive,
			isSystemWarehouse: warehouse.isSystemWarehouse,
		} as CreateWarehouseSchema,
		validators: {
			onChange: CreateWarehouseSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: warehouse.id,
					name: value.name,
					code: value.code,
					type: value.type,
					category: value.category,
					description: value.description,
					address: value.address,
					isActive: value.isActive,
					isSystemWarehouse: value.isSystemWarehouse,
				},
				{
					onSuccess: () => {
						toast.success("Almacén actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
