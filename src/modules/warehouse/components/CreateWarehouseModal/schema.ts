import * as v from "valibot";
import {
	WarehouseCategory,
	WarehouseType,
} from "../../service/model/warehouse";

export const CreateWarehouseSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	type: v.enum(WarehouseType, "Debe seleccionar un tipo válido"),
	category: v.enum(WarehouseCategory, "Debe seleccionar una categoría válida"),
	description: v.nullable(v.string()),
	address: v.nullable(v.string()),
	isActive: v.boolean(),
	isSystemWarehouse: v.boolean(),
});
export type CreateWarehouseSchema = v.InferOutput<typeof CreateWarehouseSchema>;
