import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useCreateWarehouse from "../../hooks/use-create-warehouse";
import { CreateWarehouseSchema } from "./schema";

export interface CreateWarehouseModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	code: "",
	type: "physical",
	category: "storage",
	description: null,
	address: null,
	isActive: true,
	isSystemWarehouse: false,
} as CreateWarehouseSchema;

export default function useCreateWarehouseModal({
	setIsOpen,
}: CreateWarehouseModalProps) {
	const { mutate, isPending } = useCreateWarehouse();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateWarehouseSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onSuccess: () => {
					toast.success("Almacén creado");
					handleClose();
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
