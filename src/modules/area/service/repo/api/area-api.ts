import { HttpBody } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { CreateArea, UpdateArea } from "../../model/area";
import { AreaRepository } from "../../model/repository";
import {
	AreaFromApi,
	AreaListFromApi,
	CreateAreaApiFromCreateArea,
	CreateAreaApiResponse,
	UpdateAreaApiFromUpdateArea,
} from "./dto";

const baseUrl = "/v1/areas";

const makeAreaApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(AreaListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(AreaFromApi))),
		create: (area: CreateArea) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateAreaApiFromCreateArea)(
							area,
						),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateAreaApiResponse))),
		update: (area: UpdateArea) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateAreaApiFromUpdateArea)(
							area,
						),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate/code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const areaApiRepoLive = Layer.effect(
	AreaRepository,
	makeAreaApiRepo,
);
