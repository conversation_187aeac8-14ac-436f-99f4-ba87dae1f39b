import { Schema } from "effect";
import { Area, CreateArea, UpdateArea } from "../../model/area";

export const AreaApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	delivery_days: Schema.Array(Schema.Number),
	state: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const AreaFromApi = Schema.transform(AreaApi, Area, {
	strict: true,
	decode: (areaApi) => ({
		...areaApi,
		deliveryDays: areaApi.delivery_days,
		createdAt: areaApi.created_at,
		updatedAt: areaApi.updated_at,
		deletedAt: areaApi.deleted_at,
	}),
	encode: (area) => ({
		...area,
		delivery_days: area.deliveryDays,
		created_at: area.createdAt,
		updated_at: area.updatedAt,
		deleted_at: area.deletedAt,
	}),
});

export const AreaListFromApi = Schema.Array(AreaFromApi);

export const CreateAreaApi = Schema.Struct({
	name: Schema.String,
	delivery_days: Schema.Array(Schema.Number),
	state: Schema.String,
});

export const CreateAreaApiFromCreateArea = Schema.transform(
	CreateArea,
	CreateAreaApi,
	{
		strict: true,
		decode: (createArea) => ({
			...createArea,
			delivery_days: createArea.deliveryDays,
		}),
		encode: (createAreaApi) => ({
			...createAreaApi,
			deliveryDays: createAreaApi.delivery_days,
		}),
	},
);

export const CreateAreaApiResponse = Schema.Struct({
	id: Schema.String,
});

export const UpdateAreaApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	delivery_days: Schema.Array(Schema.Number),
	state: Schema.String,
});

export const UpdateAreaApiFromUpdateArea = Schema.transform(
	UpdateArea,
	UpdateAreaApi,
	{
		strict: true,
		decode: (updateArea) => ({
			...updateArea,
			delivery_days: updateArea.deliveryDays,
		}),
		encode: (updateAreaApi) => ({
			...updateAreaApi,
			deliveryDays: updateAreaApi.delivery_days,
		}),
	},
);
