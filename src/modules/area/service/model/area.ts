import { Schema } from "effect";

export const Area = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	deliveryDays: Schema.Array(Schema.Number),
	state: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Area = typeof Area.Type;

export const CreateArea = Schema.Struct({
	name: Schema.String,
	deliveryDays: Schema.Array(Schema.Number),
	state: Schema.String,
});
export type CreateArea = typeof CreateArea.Type;

export const UpdateArea = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	deliveryDays: Schema.Array(Schema.Number),
	state: Schema.String,
});
export type UpdateArea = typeof UpdateArea.Type;
