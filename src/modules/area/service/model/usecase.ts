import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { Area, CreateArea, UpdateArea } from "./area";

export class AreaUsecase extends Effect.Tag("AreaUsecase")<
	AreaUsecase,
	{
		readonly getAll: () => Effect.Effect<Area[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Area, AppError>;
		readonly create: (area: CreateArea) => Effect.Effect<string, AppError>;
		readonly update: (area: UpdateArea) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
