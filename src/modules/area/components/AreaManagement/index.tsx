import { Plus } from "lucide-react";
import { useState } from "react";
import AreaTable from "../AreaTable";
import CreateAreaModal from "../CreateAreaModal";

export default function AreaManagement() {
	const [isCreateOpen, setIsCreateOpen] = useState(false);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Gestión de Zonas</h1>
				<button
					type="button"
					className="btn btn-primary"
					onClick={() => setIsCreateOpen(true)}
				>
					<Plus size={16} />
					Nueva Zona
				</button>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<AreaTable />
				</div>
			</div>

			<CreateAreaModal isOpen={isCreateOpen} setIsOpen={setIsCreateOpen} />
		</div>
	);
}
