import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useCreateArea from "../../hooks/use-create-area";
import { CreateAreaSchema } from "../schema";

export interface CreateAreaModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	deliveryDays: [],
	state: "",
} as CreateAreaSchema;

export default function useCreateAreaModal({
	setIsOpen,
}: CreateAreaModalProps) {
	const { mutate, isPending } = useCreateArea();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateAreaSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onSuccess: () => {
					toast.success("Zona creada");
					handleClose();
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
