import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import type { Area } from "../../service/model/area";
import DeleteAreaModal from "../DeleteAreaModal";
import EditAreaModal from "../EditAreaModal";

const columnHelper = createColumnHelper<Area>();

const dayNames = ["Dom", "Lun", "Mar", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ie", "Sáb"];

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("deliveryDays", {
		header: "Días de Entrega",
		cell: (info) => {
			const days = info.getValue();
			return (
				<div className="flex flex-wrap gap-1">
					{days.map((day) => (
						<span key={day} className="badge badge-primary badge-sm">
							{dayNames[day]}
						</span>
					))}
				</div>
			);
		},
	}),
	columnHelper.accessor("state", {
		header: "Estado",
		cell: (info) => {
			const state = info.getValue();
			return (
				<span
					className={`badge ${
						state === "active" ? "badge-success" : "badge-error"
					}`}
				>
					{state === "active" ? "Activo" : "Inactivo"}
				</span>
			);
		},
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const area = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditAreaModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={area.id}
					/>
					<DeleteAreaModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						area={area}
					/>
				</div>
			);
		},
	}),
];
