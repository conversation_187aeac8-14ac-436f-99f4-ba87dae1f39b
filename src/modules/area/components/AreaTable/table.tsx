import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "src/core/components/tables/BasicTable";
import type { Area } from "../../service/model/area";
import { columns } from "./columns";

interface Props {
	areas: Area[];
}

export default function Table({ areas }: Props) {
	const table = useReactTable({
		data: areas,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
