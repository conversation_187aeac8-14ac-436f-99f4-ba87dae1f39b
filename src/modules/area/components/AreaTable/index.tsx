import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import { areaOptions } from "../../hooks/area-options";
import Table from "./table";

export default function AreaTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(areaOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table areas={data} />;
}
