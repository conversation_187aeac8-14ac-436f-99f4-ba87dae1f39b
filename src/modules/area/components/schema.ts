import * as v from "valibot";

export const CreateAreaSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "El nombre es requerido"),
	),
	deliveryDays: v.pipe(
		v.array(v.number(), "Debe seleccionar al menos un día"),
		v.minLength(1, "Debe seleccionar al menos un día de entrega"),
	),
	state: v.pipe(
		v.string("Debe seleccionar un estado"),
		v.minLength(1, "El estado es requerido"),
	),
});

export type CreateAreaSchema = v.InferOutput<typeof CreateAreaSchema>;
