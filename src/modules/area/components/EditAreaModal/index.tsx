import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import TextModal from "src/core/components/TextModal";
import { getErrorResult } from "src/core/utils/effectErrors";
import { areaOptionsById } from "../../hooks/area-options";
import EditAreaForm from "./EditAreaForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditAreaModal({ isOpen, setIsOpen, id }: Props) {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery({
		...areaOptionsById(svc, id),
		enabled: isOpen,
	});

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError)
		return (
			<TextModal
				isOpen={isOpen}
				setIsOpen={setIsOpen}
				title="Error"
				text={getErrorResult(error).error.message}
			/>
		);

	if (isPending)
		return (
			<TextModal
				isOpen={isOpen}
				setIsOpen={setIsOpen}
				title="Cargando..."
				text="Cargando información de la zona..."
			/>
		);

	return (
		<div className={`modal ${isOpen ? "modal-open" : ""}`}>
			<div className="modal-box">
				<h3 className="font-bold text-lg">Editar Área</h3>
				<EditAreaForm area={data} setIsOpen={setIsOpen} />
			</div>
		</div>
	);
}
