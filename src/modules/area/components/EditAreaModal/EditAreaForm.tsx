import { Hash, Tag } from "lucide-react";
import type { Area } from "../../service/model/area";
import useEditAreaModal from "./use-edit-area-modal";

const dayOptions = [
	{ value: 0, label: "<PERSON>" },
	{ value: 1, label: "Lunes" },
	{ value: 2, label: "Martes" },
	{ value: 3, label: "Miércoles" },
	{ value: 4, label: "Jueves" },
	{ value: 5, label: "Viernes" },
	{ value: 6, label: "Sábado" },
];

interface Props {
	area: Area;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function EditAreaForm({ area, setIsOpen }: Props) {
	const { form, handleClose } = useEditAreaModal({
		setIsOpen,
		area,
	});

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<form.AppField
						name="name"
						children={({ FSTextField }) => (
							<FSTextField
								label="Nombre"
								placeholder="Nombre de la zona"
								prefixComponent={<Tag size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="deliveryDays"
						children={({ FSCheckboxGroupField }) => (
							<FSCheckboxGroupField
								label="Días de Entrega"
								options={dayOptions}
							/>
						)}
					/>
					<form.AppField
						name="state"
						children={({ FSSelectField }) => (
							<FSSelectField
								label="Estado"
								placeholder="Seleccionar estado"
								prefixComponent={<Hash size={16} />}
								options={[
									{ value: "active", label: "Activo" },
									{ value: "inactive", label: "Inactivo" },
								]}
							/>
						)}
					/>
				</fieldset>
				<div className="modal-action">
					<button type="button" className="btn btn-ghost" onClick={handleClose}>
						Cancelar
					</button>
					<form.SubscribeButton
						label="Actualizar"
						className="btn btn-primary"
					/>
				</div>
			</form.AppForm>
		</form>
	);
}
