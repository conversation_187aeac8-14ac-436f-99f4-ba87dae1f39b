import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const areaOptions = ({ area }: serviceRegistry) =>
	queryOptions({
		queryKey: ["areas"],
		queryFn: () => AppRuntime.runPromise(area.getAll()),
	});

export const areaOptionsById = ({ area }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["areas", id],
		queryFn: () => AppRuntime.runPromise(area.getById(id)),
	});
