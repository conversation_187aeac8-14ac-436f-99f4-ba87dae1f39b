import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Area, CreateArea } from "../service/model/area";
import { areaOptions } from "./area-options";

export default function useCreateArea() {
	const service = useService();
	const { area } = service;
	const queryClient = useQueryClient();
	const queryKey = areaOptions(service).queryKey;

	return useMutation({
		mutationFn: (newArea: CreateArea) =>
			AppRuntime.runPromise(area.create(newArea)),
		onMutate: async (newArea) => {
			await queryClient.cancelQueries({ queryKey });
			const previousAreas = queryClient.getQueryData<Area[]>(queryKey);

			queryClient.setQueryData<Area[]>(queryKey, (old) =>
				create(old || [], (draft) => {
					draft.push({
						id: "temp-id",
						name: newArea.name,
						deliveryDays: newArea.deliveryDays,
						state: newArea.state,
						createdAt: new Date().toISOString(),
						updatedAt: new Date().toISOString(),
						deletedAt: null,
					});
				}),
			);

			return { previousAreas };
		},
		onError: (_err, _newArea, context) => {
			if (context?.previousAreas) {
				queryClient.setQueryData(queryKey, context.previousAreas);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
