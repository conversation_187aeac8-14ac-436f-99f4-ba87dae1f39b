import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Area } from "../service/model/area";
import { areaOptions } from "./area-options";

export default function useDeleteArea() {
	const service = useService();
	const { area } = service;
	const queryClient = useQueryClient();
	const queryKey = areaOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(area.delete(id)),
		onMutate: async (deletedId) => {
			await queryClient.cancelQueries({ queryKey });
			const previousAreas = queryClient.getQueryData<Area[]>(queryKey);

			queryClient.setQueryData<Area[]>(queryKey, (old) =>
				create(old || [], (draft) => {
					const index = draft.findIndex((item) => item.id === deletedId);
					if (index !== -1) {
						draft.splice(index, 1);
					}
				}),
			);

			return { previousAreas };
		},
		onError: (_err, _deletedId, context) => {
			if (context?.previousAreas) {
				queryClient.setQueryData(queryKey, context.previousAreas);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
