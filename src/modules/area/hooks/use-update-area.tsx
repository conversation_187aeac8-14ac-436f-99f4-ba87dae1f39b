import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Area, UpdateArea } from "../service/model/area";
import { areaOptions } from "./area-options";

export default function useUpdateArea() {
	const service = useService();
	const { area } = service;
	const queryClient = useQueryClient();
	const queryKey = areaOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedArea: UpdateArea) =>
			AppRuntime.runPromise(area.update(updatedArea)),
		onMutate: async (updatedArea) => {
			await queryClient.cancelQueries({ queryKey });
			const previousAreas = queryClient.getQueryData<Area[]>(queryKey);

			queryClient.setQueryData<Area[]>(queryKey, (old) =>
				create(old || [], (draft) => {
					const index = draft.findIndex((item) => item.id === updatedArea.id);
					if (index !== -1) {
						draft[index] = {
							...draft[index],
							name: updatedArea.name,
							deliveryDays: updatedArea.deliveryDays,
							state: updatedArea.state,
							updatedAt: new Date().toISOString(),
						};
					}
				}),
			);

			return { previousAreas };
		},
		onError: (_err, _updatedArea, context) => {
			if (context?.previousAreas) {
				queryClient.setQueryData(queryKey, context.previousAreas);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
