import { Schema } from "effect";
import {
	CreateMeasurementUnit,
	MeasurementUnit,
	UpdateMeasurementUnit,
} from "../../model/measurement-unit";

export const MeasurementUnitApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	unit_measurement_category_id: Schema.NullOr(Schema.String),
	abbreviation: Schema.NullOr(Schema.String),
	type: Schema.NullOr(Schema.String),
	conversion_factor: Schema.NullOr(Schema.Number),
	state: Schema.NullOr(Schema.String),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const MeasurementUnitFromApi = Schema.transform(
	MeasurementUnitApi,
	MeasurementUnit,
	{
		strict: true,
		decode: (measurementUnitApi) => ({
			id: measurementUnitApi.id,
			name: measurementUnitApi.name,
			code: measurementUnitApi.code,
			unitMeasurementCategoryId:
				measurementUnitApi.unit_measurement_category_id,
			abbreviation: measurementUnitApi.abbreviation,
			type: measurementUnitApi.type,
			conversionFactor: measurementUnitApi.conversion_factor,
			state: measurementUnitApi.state,
			createdAt: measurementUnitApi.created_at,
			updatedAt: measurementUnitApi.updated_at,
			deletedAt: measurementUnitApi.deleted_at,
		}),
		encode: (measurementUnit) => ({
			id: measurementUnit.id,
			name: measurementUnit.name,
			code: measurementUnit.code,
			unit_measurement_category_id: measurementUnit.unitMeasurementCategoryId,
			abbreviation: measurementUnit.abbreviation,
			type: measurementUnit.type,
			conversion_factor: measurementUnit.conversionFactor,
			state: measurementUnit.state,
			created_at: measurementUnit.createdAt,
			updated_at: measurementUnit.updatedAt,
			deleted_at: measurementUnit.deletedAt,
		}),
	},
);

export const MeasurementUnitListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(MeasurementUnitFromApi))),
	Schema.mutable(Schema.Array(MeasurementUnit)),
	{
		strict: true,
		decode: (measurementUnitApiList) =>
			measurementUnitApiList ? measurementUnitApiList : [],
		encode: (measurementUnitList) => measurementUnitList,
	},
);

export const CreateMeasurementUnitApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	unit_measurement_category_id: Schema.optional(Schema.String),
	abbreviation: Schema.optional(Schema.String),
	type: Schema.optional(Schema.String),
	conversion_factor: Schema.optional(Schema.Number),
	state: Schema.optional(Schema.String),
});

export const CreateMeasurementUnitApiFromCreateMeasurementUnit =
	Schema.transform(CreateMeasurementUnit, CreateMeasurementUnitApi, {
		strict: true,
		decode: (createMeasurementUnit) => ({
			name: createMeasurementUnit.name,
			code: createMeasurementUnit.code,
			unit_measurement_category_id:
				createMeasurementUnit.unitMeasurementCategoryId,
			abbreviation: createMeasurementUnit.abbreviation,
			type: createMeasurementUnit.type,
			conversion_factor: createMeasurementUnit.conversionFactor,
			state: createMeasurementUnit.state,
		}),
		encode: (createMeasurementUnitApi) => ({
			name: createMeasurementUnitApi.name,
			code: createMeasurementUnitApi.code,
			unitMeasurementCategoryId:
				createMeasurementUnitApi.unit_measurement_category_id,
			abbreviation: createMeasurementUnitApi.abbreviation,
			type: createMeasurementUnitApi.type,
			conversionFactor: createMeasurementUnitApi.conversion_factor,
			state: createMeasurementUnitApi.state,
		}),
	});

export const UpdateMeasurementUnitApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	unit_measurement_category_id: Schema.optional(Schema.String),
	abbreviation: Schema.optional(Schema.String),
	type: Schema.optional(Schema.String),
	conversion_factor: Schema.optional(Schema.Number),
	state: Schema.optional(Schema.String),
});

export const UpdateMeasurementUnitApiFromUpdateMeasurementUnit =
	Schema.transform(UpdateMeasurementUnit, UpdateMeasurementUnitApi, {
		strict: true,
		decode: (updateMeasurementUnit) => ({
			id: updateMeasurementUnit.id,
			name: updateMeasurementUnit.name,
			code: updateMeasurementUnit.code,
			unit_measurement_category_id:
				updateMeasurementUnit.unitMeasurementCategoryId,
			abbreviation: updateMeasurementUnit.abbreviation,
			type: updateMeasurementUnit.type,
			conversion_factor: updateMeasurementUnit.conversionFactor,
			state: updateMeasurementUnit.state,
		}),
		encode: (updateMeasurementUnitApi) => ({
			id: updateMeasurementUnitApi.id,
			name: updateMeasurementUnitApi.name,
			code: updateMeasurementUnitApi.code,
			unitMeasurementCategoryId:
				updateMeasurementUnitApi.unit_measurement_category_id,
			abbreviation: updateMeasurementUnitApi.abbreviation,
			type: updateMeasurementUnitApi.type,
			conversionFactor: updateMeasurementUnitApi.conversion_factor,
			state: updateMeasurementUnitApi.state,
		}),
	});

export const CreateMeasurementUnitApiResponse = Schema.String;
