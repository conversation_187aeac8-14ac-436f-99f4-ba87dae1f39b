import { Schema } from "effect";
import { UnitMeasurementCategory } from "../../model/measurement-unit-categories";

export const UnitMeasurementCategoryApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const UnitMeasurementCategoryFromApi = Schema.transform(
	UnitMeasurementCategoryApi,
	UnitMeasurementCategory,
	{
		strict: true,
		decode: (unitMeasurementCategoryApi) => ({
			...unitMeasurementCategoryApi,
			createdAt: unitMeasurementCategoryApi.created_at,
			updatedAt: unitMeasurementCategoryApi.updated_at,
			deletedAt: unitMeasurementCategoryApi.deleted_at,
		}),
		encode: (unitMeasurementCategory) => ({
			...unitMeasurementCategory,
			created_at: unitMeasurementCategory.createdAt,
			updated_at: unitMeasurementCategory.updatedAt,
			deleted_at: unitMeasurementCategory.deletedAt,
		}),
	},
);

export const UnitMeasurementCategoryListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(UnitMeasurementCategoryFromApi))),
	Schema.mutable(Schema.Array(UnitMeasurementCategory)),
	{
		strict: true,
		decode: (unitMeasurementCategoryApiList) => (unitMeasurementCategoryApiList ? unitMeasurementCategoryApiList : []),
		encode: (unitMeasurementCategoryList) => unitMeasurementCategoryList,
	},
);
