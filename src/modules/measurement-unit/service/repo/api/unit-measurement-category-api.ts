import { Effect, Layer } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import { handleDResponse } from "src/core/service/repo/api/utils";
import { UnitMeasurementCategoryRepository } from "../../model/unit-measurement-category-repository";
import { UnitMeasurementCategoryListFromApi } from "./unit-measurement-category-dto";

const baseUrl = "/v1/unit-measurement-categories";

const makeUnitMeasurementCategoryApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(UnitMeasurementCategoryListFromApi))),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const unitMeasurementCategoryApiRepoLive = Layer.effect(
	UnitMeasurementCategoryRepository,
	makeUnitMeasurementCategoryApiRepo,
);
