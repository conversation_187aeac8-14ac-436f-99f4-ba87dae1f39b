import { Schema } from "effect";

export const MeasurementUnit = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	unitMeasurementCategoryId: Schema.NullOr(Schema.String),
	abbreviation: Schema.NullOr(Schema.String),
	type: Schema.NullOr(Schema.String),
	conversionFactor: Schema.NullOr(Schema.Number),
	state: Schema.NullOr(Schema.String),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type MeasurementUnit = typeof MeasurementUnit.Type;

export const CreateMeasurementUnit = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	unitMeasurementCategoryId: Schema.optional(Schema.String),
	abbreviation: Schema.optional(Schema.String),
	type: Schema.optional(Schema.String),
	conversionFactor: Schema.optional(Schema.Number),
	state: Schema.optional(Schema.String),
});
export type CreateMeasurementUnit = typeof CreateMeasurementUnit.Type;

export const UpdateMeasurementUnit = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	unitMeasurementCategoryId: Schema.optional(Schema.String),
	abbreviation: Schema.optional(Schema.String),
	type: Schema.optional(Schema.String),
	conversionFactor: Schema.optional(Schema.Number),
	state: Schema.optional(Schema.String),
});
export type UpdateMeasurementUnit = typeof UpdateMeasurementUnit.Type;
