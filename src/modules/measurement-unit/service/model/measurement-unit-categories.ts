import { Schema } from "effect";

export const UnitMeasurementCategory = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type UnitMeasurementCategory = typeof UnitMeasurementCategory.Type;

export enum UnitMeasurementCategoryCode {
	UNIT = "UNIT",
	WEIGHT = "WEIGHT",
	LIQUID_VOLUME = "LIQUID_VOLUME",
	LENGTH = "LENGTH",
}
