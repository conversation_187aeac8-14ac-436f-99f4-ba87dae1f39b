import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { UnitMeasurementCategory } from "./measurement-unit-categories";

export class UnitMeasurementCategoryRepository extends Effect.Tag(
	"UnitMeasurementCategoryRepository",
)<
	UnitMeasurementCategoryRepository,
	{
		readonly getAll: () => Effect.Effect<UnitMeasurementCategory[], AppError>;
	}
>() {}
