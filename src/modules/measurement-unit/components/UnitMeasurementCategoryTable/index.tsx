import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { useService } from "src/config/context/serviceProvider";
import { measurementUnitOptions } from "../../hooks/measurement-unit-options";
import { unitMeasurementCategoryOptions } from "../../hooks/unit-measurement-category-options";
import type { UnitMeasurementCategoryWithUnits } from "./columns";
import Table from "./table";

export default function UnitMeasurementCategoryTable() {
	const service = useService();

	const { data: unitMeasurementCategories = [], isLoading: categoriesLoading } =
		useQuery(unitMeasurementCategoryOptions(service));

	const { data: measurementUnits = [], isLoading: unitsLoading } = useQuery(
		measurementUnitOptions(service),
	);

	const categoriesWithUnits = useMemo<
		UnitMeasurementCategoryWithUnits[]
	>(() => {
		return unitMeasurementCategories.map((category) => ({
			...category,
			measurementUnits: measurementUnits.filter(
				(unit) => unit.unitMeasurementCategoryId === category.id,
			),
		}));
	}, [unitMeasurementCategories, measurementUnits]);

	if (categoriesLoading || unitsLoading) {
		return (
			<div className="flex h-32 items-center justify-center">
				<span className="loading loading-spinner loading-lg" />
			</div>
		);
	}

	return (
		<div className="overflow-x-auto">
			<Table categoriesWithUnits={categoriesWithUnits} />
		</div>
	);
}
