import { useQuery } from "@tanstack/react-query";
import { Calculator, Hash, Ruler, Tag } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { cn } from "src/core/utils/classes";
import { unitMeasurementCategoryOptions } from "../../hooks/unit-measurement-category-options";
import type { CreateMeasurementUnitModalProps } from "./use-create-modal";
import useCreateMeasurementUnitModal from "./use-create-modal";

export default function CreateMeasurementUnitModal({
	isOpen,
	setIsOpen,
}: CreateMeasurementUnitModalProps) {
	const service = useService();
	const { measurementUnit } = service;
	const { form, handleClose, isPending } = useCreateMeasurementUnitModal({
		isOpen,
		setIsOpen,
	});

	const { data: unitMeasurementCategories = [] } = useQuery(
		unitMeasurementCategoryOptions(service),
	);

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Crear Unidad de Medida</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la unidad de medida"
										prefixComponent={<Ruler size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(
												measurementUnit.validateCode(value),
											);
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la unidad de medida"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="unitMeasurementCategoryId"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Categoría de Unidad"
										placeholder="Seleccionar categoría"
										options={unitMeasurementCategories.map((category) => ({
											value: category.id,
											label: category.name,
										}))}
									/>
								)}
							/>
							<form.AppField
								name="abbreviation"
								children={({ FSTextField }) => (
									<FSTextField
										label="Abreviación"
										placeholder="Abreviación de la unidad"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="type"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Tipo"
										placeholder="Seleccionar tipo"
										options={[
											{ value: "less", label: "Menor" },
											{ value: "base", label: "Base" },
											{ value: "greater", label: "Mayor" },
										]}
									/>
								)}
							/>
							<form.AppField
								name="conversionFactor"
								children={({ FSTextField }) => (
									<FSTextField
										label="Factor de Conversión"
										placeholder="Factor de conversión"
										type="number"
										prefixComponent={<Calculator size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="state"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Estado"
										placeholder="Seleccionar estado"
										options={[
											{ value: "active", label: "Activo" },
											{ value: "inactive", label: "Inactivo" },
										]}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
