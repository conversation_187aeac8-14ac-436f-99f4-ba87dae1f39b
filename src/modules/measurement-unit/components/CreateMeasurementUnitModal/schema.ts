import * as v from "valibot";

export const CreateMeasurementUnitSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	unitMeasurementCategoryId: v.optional(v.string()),
	abbreviation: v.optional(v.string()),
	type: v.optional(v.string()),
	conversionFactor: v.optional(v.number()),
	state: v.optional(v.string()),
});
export type CreateMeasurementUnitSchema = v.InferOutput<
	typeof CreateMeasurementUnitSchema
>;
