import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useCreateMeasurementUnit from "../../hooks/use-create-measurement-unit";
import { CreateMeasurementUnitSchema } from "./schema";

export interface CreateMeasurementUnitModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	code: "",
	unitMeasurementCategoryId: undefined,
	abbreviation: undefined,
	type: undefined,
	conversionFactor: undefined,
	state: undefined,
} as CreateMeasurementUnitSchema;

export default function useCreateMeasurementUnitModal({
	setIsOpen,
}: CreateMeasurementUnitModalProps) {
	const { mutate, isPending } = useCreateMeasurementUnit();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateMeasurementUnitSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
				onSettled: () => {
					toast.success("Unidad de medida creada");
					handleClose();
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
