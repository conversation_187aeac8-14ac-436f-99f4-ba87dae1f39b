import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "src/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const unitMeasurementCategoryOptions = ({ unitMeasurementCategory }: serviceRegistry) =>
	queryOptions({
		queryKey: ["unit-measurement-categories"],
		queryFn: () => AppRuntime.runPromise(unitMeasurementCategory.getAll()),
	});
