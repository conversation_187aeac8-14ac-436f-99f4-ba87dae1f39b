export interface ProductOrderItem {
	productId: string;
	quantity: number;
}

export interface RecipeCalculation {
	recipeId: string;
	recipeName: string;
	recipeType: string;
	batchSize: number;
	batchesNeeded: number;
	productsInRecipe: Array<{
		productId: string;
		productName: string;
		requestedQuantity: number;
	}>;
	components: Array<{
		productId: string;
		productName: string;
		quantityPerBatch: number;
		totalQuantity: number;
	}>;
}

export interface ProductMaterial {
	productId: string;
	productName: string;
	materialId: string;
	materialName: string;
	quantityPerUnit: number;
	totalQuantity: number;
	requestedQuantity: number;
}

export interface MultiProductCalculation {
	recipes: RecipeCalculation[];
	productMaterials: ProductMaterial[];
	aggregatedComponents: Array<{
		productId: string;
		productName: string;
		totalQuantity: number;
		sources: Array<{
			recipeId: string;
			recipeName: string;
			quantity: number;
		}>;
	}>;
	aggregatedMaterials: Array<{
		materialId: string;
		materialName: string;
		totalQuantity: number;
		sources: Array<{
			productId: string;
			productName: string;
			quantity: number;
		}>;
	}>;
}
