import * as v from "valibot";

export const ProductOrderItemSchema = v.object({
	productId: v.pipe(
		v.string("Debe seleccionar un producto"),
		v.minLength(1, "Debe seleccionar un producto"),
	),
	quantity: v.pipe(
		v.number("Debe ingresar una cantidad"),
		v.minValue(0, "La cantidad debe ser mayor a 0"),
	),
});

export const ProductionOrderSimulationSchema = v.object({
	products: v.pipe(
		v.array(ProductOrderItemSchema),
		v.minLength(1, "Debe agregar al menos un producto"),
	),
});

export type ProductOrderItemSchema = v.InferOutput<
	typeof ProductOrderItemSchema
>;
export type ProductionOrderSimulationSchema = v.InferOutput<
	typeof ProductionOrderSimulationSchema
>;
