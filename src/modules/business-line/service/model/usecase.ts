import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type {
	BusinessLine,
	BusinessLineCreate,
	BusinessLineUpdate,
	BusinessLineWithSublines,
} from "./business-line";

export class BusinessLineUsecase extends Effect.Tag("BusinessLineUsecase")<
	BusinessLineUsecase,
	{
		readonly getAll: () => Effect.Effect<BusinessLine[], AppError>;
		readonly getById: (id: string) => Effect.Effect<BusinessLine, AppError>;
		readonly create: (
			businessLine: BusinessLineCreate,
		) => Effect.Effect<string, AppError>;
		readonly update: (
			businessLine: BusinessLineUpdate,
		) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly getSublines: (
			id: string,
		) => Effect.Effect<BusinessLine[], AppError>;
		readonly getDetails: (
			id: string,
		) => Effect.Effect<BusinessLineWithSublines, AppError>;
		readonly getParentLines: () => Effect.Effect<BusinessLine[], AppError>;
		readonly getSublinesByCode: (
			code: string,
		) => Effect.Effect<BusinessLine[], AppError>;
		readonly getBusinessLineWithDetailsByCode: (
			code: string,
		) => Effect.Effect<BusinessLineWithSublines, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
