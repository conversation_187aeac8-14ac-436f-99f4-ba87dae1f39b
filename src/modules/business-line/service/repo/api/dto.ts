import { Schema } from "effect";
import {
	BusinessLine,
	BusinessLineCreate,
	BusinessLineUpdate,
	BusinessLineWithSublines,
} from "../../model/business-line";

export const BusinessLineApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	state: Schema.String,
	parent_id: Schema.NullOr(Schema.String),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const BusinessLineFromApi = Schema.transform(
	BusinessLineApi,
	BusinessLine,
	{
		strict: true,
		decode: (businessLineApi) => ({
			...businessLineApi,
			parentId: businessLineApi.parent_id,
			createdAt: businessLineApi.created_at,
			updatedAt: businessLineApi.updated_at,
			deletedAt: businessLineApi.deleted_at,
		}),
		encode: (businessLine) => ({
			...businessLine,
			parent_id: businessLine.parentId,
			created_at: businessLine.createdAt,
			updated_at: businessLine.updatedAt,
			deleted_at: businessLine.deletedAt,
		}),
	},
);

export const BusinessLineListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(BusinessLineFromApi))),
	Schema.mutable(Schema.Array(BusinessLine)),
	{
		strict: true,
		decode: (businessLineApiList) =>
			businessLineApiList ? businessLineApiList : [],
		encode: (businessLineList) => businessLineList,
	},
);

export const CreateBusinessLineApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	state: Schema.String,
	parent_id: Schema.optional(Schema.String),
});

export const CreateBusinessLineApiFromCreateBusinessLine = Schema.transform(
	BusinessLineCreate,
	CreateBusinessLineApi,
	{
		strict: true,
		decode: (createBusinessLine) => ({
			name: createBusinessLine.name,
			code: createBusinessLine.code,
			state: createBusinessLine.state,
			parent_id: createBusinessLine.parentId,
		}),
		encode: (createBusinessLineApi) => ({
			name: createBusinessLineApi.name,
			code: createBusinessLineApi.code,
			state: createBusinessLineApi.state,
			parentId: createBusinessLineApi.parent_id,
		}),
	},
);

export const UpdateBusinessLineApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	state: Schema.String,
	parent_id: Schema.optional(Schema.String),
});

export const UpdateBusinessLineApiFromUpdateBusinessLine = Schema.transform(
	BusinessLineUpdate,
	UpdateBusinessLineApi,
	{
		strict: true,
		decode: (updateBusinessLine) => ({
			id: updateBusinessLine.id,
			name: updateBusinessLine.name,
			code: updateBusinessLine.code,
			state: updateBusinessLine.state,
			parent_id: updateBusinessLine.parentId,
		}),
		encode: (updateBusinessLineApi) => ({
			id: updateBusinessLineApi.id,
			name: updateBusinessLineApi.name,
			code: updateBusinessLineApi.code,
			state: updateBusinessLineApi.state,
			parentId: updateBusinessLineApi.parent_id,
		}),
	},
);

export const BusinessLineWithSublinesApi = Schema.Struct({
	business_line: BusinessLineFromApi,
	sublines: Schema.Array(BusinessLineFromApi),
	parent: Schema.NullishOr(BusinessLineFromApi),
});

export const BusinessLineWithSublinesFromApi = Schema.transform(
	BusinessLineWithSublinesApi,
	BusinessLineWithSublines,
	{
		strict: true,
		decode: (businessLineWithSublinesApi) => ({
			businessLine: businessLineWithSublinesApi.business_line,
			sublines: businessLineWithSublinesApi.sublines,
			parent: businessLineWithSublinesApi.parent,
		}),
		encode: (businessLineWithSublines) => ({
			business_line: businessLineWithSublines.businessLine,
			sublines: businessLineWithSublines.sublines,
			parent: businessLineWithSublines.parent,
		}),
	},
);

export const CreateBusinessLineApiResponse = Schema.String;
