import { HttpB<PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import {
	handleDResponse,
	handleResponse,
} from "src/core/service/repo/api/utils";
import type { BusinessLineCreate, BusinessLineUpdate } from "../../model/business-line";
import { BusinessLineRepository } from "../../model/repository";
import {
	BusinessLineFromApi,
	BusinessLineListFromApi,
	BusinessLineWithSublinesFromApi,
	CreateBusinessLineApiFromCreateBusinessLine,
	CreateBusinessLineApiResponse,
	UpdateBusinessLineApiFromUpdateBusinessLine,
} from "./dto";

const baseUrl = "/v1/business-lines";

const makeBusinessLineApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(BusinessLineListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(BusinessLineFromApi))),
		create: (businessLine: BusinessLineCreate) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateBusinessLineApiFromCreateBusinessLine)(businessLine),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateBusinessLineApiResponse))),
		update: (businessLine: BusinessLineUpdate) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateBusinessLineApiFromUpdateBusinessLine)(businessLine),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		getSublines: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}/sublines`)
				.pipe(Effect.flatMap(handleDResponse(BusinessLineListFromApi))),
		getDetails: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}/details`)
				.pipe(
					Effect.flatMap(handleDResponse(BusinessLineWithSublinesFromApi)),
				),
		getParentLines: () =>
			httpClient
				.get(`${baseUrl}/query/parent-lines`)
				.pipe(Effect.flatMap(handleDResponse(BusinessLineListFromApi))),
		getSublinesByCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/query/by-code/${code}/sublines`)
				.pipe(Effect.flatMap(handleDResponse(BusinessLineListFromApi))),
		getBusinessLineWithDetailsByCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/query/by-code/${code}/details`)
				.pipe(
					Effect.flatMap(handleDResponse(BusinessLineWithSublinesFromApi)),
				),
		validateCode: (code: string) =>
			httpClient
				.get(`${baseUrl}/validate/code/${code}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const businessLineApiRepoLive = Layer.effect(BusinessLineRepository, makeBusinessLineApiRepo);
