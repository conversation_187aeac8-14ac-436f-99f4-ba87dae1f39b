import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import { businessLineDetailsOptions } from "../../hooks/business-line-options";
import BusinessLineTable from "../BusinessLineTable";
import CreateBusinessLineModal from "../CreateBusinessLineModal";

interface BusinessLineManagementProps {
	parentId?: string;
}

export default function BusinessLineManagement({
	parentId,
}: BusinessLineManagementProps) {
	const [isCreateOpen, setIsCreateOpen] = useState(false);
	const svc = useService();

	// Get parent details if parentId is provided
	const {
		data: parentDetails,
		isError: isParentError,
		error: parentError,
	} = useQuery({
		...businessLineDetailsOptions(svc, parentId || ""),
		enabled: !!parentId,
	});

	const isParentView = !!parentId;
	const title = isParentView
		? `Sub-giros de Negocio - ${parentDetails?.businessLine.name || "Cargando..."}`
		: "Gestión de Giros de Negocio";

	const buttonText = isParentView ? "Nuevo Sub-giro" : "Nuevo Giro de Negocio";

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		if (isParentError) {
			console.log(getErrorResult(parentError).error);
		}
	}, [isParentError]);

	if (isParentView && isParentError) {
		return <div>Error: {getErrorResult(parentError).error.message}</div>;
	}

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					{isParentView && (
						<button
							type="button"
							className="btn btn-ghost btn-sm"
							onClick={() => window.history.back()}
						>
							<ArrowLeft size={16} />
							Volver
						</button>
					)}
					<h1 className="font-bold text-2xl">{title}</h1>
				</div>
				<button
					type="button"
					className="btn btn-primary"
					onClick={() => setIsCreateOpen(true)}
				>
					<Plus size={16} />
					{buttonText}
				</button>
			</div>

			{isParentView && parentDetails && (
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<h2 className="card-title">Información del Giro Padre</h2>
						<div className="grid grid-cols-2 gap-4">
							<div>
								<span className="font-semibold">Nombre:</span>{" "}
								{parentDetails.businessLine.name}
							</div>
							<div>
								<span className="font-semibold">Código:</span>{" "}
								{parentDetails.businessLine.code}
							</div>
						</div>
					</div>
				</div>
			)}

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<BusinessLineTable parentId={parentId} />
				</div>
			</div>

			<CreateBusinessLineModal
				isOpen={isCreateOpen}
				setIsOpen={setIsCreateOpen}
				parentId={parentId}
			/>
		</div>
	);
}
