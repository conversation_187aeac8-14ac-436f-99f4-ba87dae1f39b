import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useCreateBusinessLine from "../../hooks/use-create-business-line";
import { CreateBusinessLineSchema } from "../schema";

export interface CreateBusinessLineModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	parentId?: string;
}

const defaultValues: CreateBusinessLineSchema = {
	name: "",
	code: "",
	state: "active",
};

export default function useCreateBusinessLineModal({
	setIsOpen,
	parentId,
}: CreateBusinessLineModalProps) {
	const { mutate, isPending } = useCreateBusinessLine(parentId);

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateBusinessLineSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					...value,
					parentId,
				},
				{
					onSuccess: () => {
						toast.success("Giro de negocio creado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
