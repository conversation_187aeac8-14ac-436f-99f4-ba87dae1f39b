import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "src/core/components/tables/BasicTable";
import type { BusinessLine } from "../../service/model/business-line";
import { columns } from "./columns";

interface TableProps {
	businessLines: BusinessLine[];
}

export default function Table({ businessLines }: TableProps) {
	const table = useReactTable({
		data: businessLines,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});

	return <BasicTable table={table} />;
}
