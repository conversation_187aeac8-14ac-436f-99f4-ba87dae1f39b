import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import { businessLineParentsOptions, businessLineSublinesOptions } from "../../hooks/business-line-options";
import Table from "./table";

interface BusinessLineTableProps {
	parentId?: string;
}

export default function BusinessLineTable({ parentId }: BusinessLineTableProps) {
	const svc = useService();

	const queryOptions = parentId 
		? businessLineSublinesOptions(svc, parentId)
		: businessLineParentsOptions(svc);

	const { data, isError, error, isPending } = useQuery(queryOptions);

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table businessLines={data} />;
}
