import { Link } from "@tanstack/react-router";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Eye, Trash2 } from "lucide-react";
import { useState } from "react";
import useDeleteBusinessLine from "../../hooks/use-delete-business-line";
import type { BusinessLine } from "../../service/model/business-line";
import EditBusinessLineModal from "../EditBusinessLineModal";

export const columns: ColumnDef<BusinessLine>[] = [
	{
		accessorKey: "name",
		header: "Nombre",
	},
	{
		accessorKey: "code",
		header: "Código",
	},
	{
		accessorKey: "state",
		header: "Estado",
		cell: ({ row }) => {
			const state = row.original.state;
			return (
				<span
					className={`badge ${state === "active" ? "badge-success" : "badge-error"}`}
				>
					{state === "active" ? "Activo" : "Inactivo"}
				</span>
			);
		},
	},
	{
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const businessLine = row.original;
			const [isEditOpen, setIsEditOpen] = useState(false);
			const { mutate: deleteBusinessLine } = useDeleteBusinessLine(
				businessLine.parentId || undefined,
			);

			const handleDelete = () => {
				if (
					confirm("¿Está seguro de que desea eliminar este giro de negocio?")
				) {
					deleteBusinessLine(businessLine.id);
				}
			};

			return (
				<div className="flex gap-2">
					{!businessLine.parentId && (
						<Link
							to="/admin/settings/business-lines/$parentId"
							params={{ parentId: businessLine.id }}
							className="btn btn-ghost btn-sm"
						>
							<Eye size={16} />
						</Link>
					)}
					<button
						type="button"
						className="btn btn-ghost btn-sm"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-ghost btn-sm text-error"
						onClick={handleDelete}
					>
						<Trash2 size={16} />
					</button>
					<EditBusinessLineModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={businessLine.id}
						parentId={businessLine.parentId || undefined}
					/>
				</div>
			);
		},
	},
];
