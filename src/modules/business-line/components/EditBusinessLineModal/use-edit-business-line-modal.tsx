import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useUpdateBusinessLine from "../../hooks/use-update-business-line";
import type { BusinessLine } from "../../service/model/business-line";
import { CreateBusinessLineSchema } from "../schema";

export interface EditBusinessLineModalProps {
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	businessLine: BusinessLine;
	parentId?: string;
}

export default function useEditBusinessLineModal({
	setIsOpen,
	businessLine,
	parentId,
}: EditBusinessLineModalProps) {
	const { mutate } = useUpdateBusinessLine(parentId);

	const form = useAppForm({
		defaultValues: {
			name: businessLine.name,
			code: businessLine.code,
			state: businessLine.state,
		} as CreateBusinessLineSchema,
		validators: {
			onChange: CreateBusinessLineSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: businessLine.id,
					name: value.name,
					code: value.code,
					state: value.state,
					parentId,
				},
				{
					onSuccess: () => {
						toast.success("Giro de negocio actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
