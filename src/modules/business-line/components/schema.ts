import * as v from "valibot";

export const CreateBusinessLineSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "El nombre es requerido"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "El código es requerido"),
	),
	state: v.pipe(
		v.string("Debe seleccionar un estado"),
		v.minLength(1, "El estado es requerido"),
	),
});

export type CreateBusinessLineSchema = v.InferOutput<
	typeof CreateBusinessLineSchema
>;
