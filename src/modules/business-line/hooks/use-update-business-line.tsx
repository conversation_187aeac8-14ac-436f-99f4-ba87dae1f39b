import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type {
	BusinessLine,
	BusinessLineUpdate,
} from "../service/model/business-line";
import {
	businessLineOptions,
	businessLineParentsOptions,
	businessLineSublinesOptions,
} from "./business-line-options";

export default function useUpdateBusinessLine(parentId?: string) {
	const service = useService();
	const { businessLine } = service;
	const queryClient = useQueryClient();

	const queryKey = parentId
		? businessLineSublinesOptions(service, parentId).queryKey
		: businessLineParentsOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedBusinessLine: BusinessLineUpdate) =>
			AppRuntime.runPromise(businessLine.update(updatedBusinessLine)),
		onMutate: async (updatedBusinessLine) => {
			await queryClient.cancelQueries({ queryKey });

			const previousData = queryClient.getQueryData<BusinessLine[]>(queryKey);

			queryClient.setQueryData<BusinessLine[]>(queryKey, (old) =>
				create(old || [], (draft) => {
					const index = draft.findIndex(
						(item) => item.id === updatedBusinessLine.id,
					);
					if (index !== -1) {
						draft[index] = {
							...draft[index],
							name: updatedBusinessLine.name,
							code: updatedBusinessLine.code,
							state: updatedBusinessLine.state,
							parentId: updatedBusinessLine.parentId || null,
							updatedAt: new Date().toISOString(),
						};
					}
				}),
			);

			return { previousData };
		},
		onError: (_error, _variables, context) => {
			if (context?.previousData) {
				queryClient.setQueryData(queryKey, context.previousData);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
			queryClient.invalidateQueries({
				queryKey: businessLineOptions(service).queryKey,
			});
		},
	});
}
