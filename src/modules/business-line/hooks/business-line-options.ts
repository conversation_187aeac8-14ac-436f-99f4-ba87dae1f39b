import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const businessLineOptions = ({ businessLine }: serviceRegistry) =>
	queryOptions({
		queryKey: ["business-lines"],
		queryFn: () => AppRuntime.runPromise(businessLine.getAll()),
	});

export const businessLineOptionsById = (
	{ businessLine }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["business-lines", id],
		queryFn: () => AppRuntime.runPromise(businessLine.getById(id)),
	});

export const businessLineSublinesOptions = (
	{ businessLine }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["business-lines", id, "sublines"],
		queryFn: () => AppRuntime.runPromise(businessLine.getSublines(id)),
	});

export const businessLineDetailsOptions = (
	{ businessLine }: serviceRegistry,
	id: string,
) =>
	queryOptions({
		queryKey: ["business-lines", id, "details"],
		queryFn: () => AppRuntime.runPromise(businessLine.getDetails(id)),
	});

export const businessLineParentsOptions = ({ businessLine }: serviceRegistry) =>
	queryOptions({
		queryKey: ["business-lines", "parents"],
		queryFn: () => AppRuntime.runPromise(businessLine.getParentLines()),
	});

export const businessLineSublinesByCodeOptions = (
	{ businessLine }: serviceRegistry,
	code: string,
) =>
	queryOptions({
		queryKey: ["business-lines", "by-code", code, "sublines"],
		queryFn: () => AppRuntime.runPromise(businessLine.getSublinesByCode(code)),
	});

export const businessLineDetailsByCodeOptions = (
	{ businessLine }: serviceRegistry,
	code: string,
) =>
	queryOptions({
		queryKey: ["business-lines", "by-code", code, "details"],
		queryFn: () => AppRuntime.runPromise(businessLine.getBusinessLineWithDetailsByCode(code)),
	});
