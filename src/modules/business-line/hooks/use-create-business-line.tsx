import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type {
	BusinessLine,
	BusinessLineCreate,
} from "../service/model/business-line";
import {
	businessLineOptions,
	businessLineParentsOptions,
	businessLineSublinesOptions,
} from "./business-line-options";

export default function useCreateBusinessLine(parentId?: string) {
	const service = useService();
	const { businessLine } = service;
	const queryClient = useQueryClient();

	const queryKey = parentId
		? businessLineSublinesOptions(service, parentId).queryKey
		: businessLineParentsOptions(service).queryKey;

	return useMutation({
		mutationFn: (newBusinessLine: BusinessLineCreate) =>
			AppRuntime.runPromise(businessLine.create(newBusinessLine)),
		onMutate: async (newBusinessLine) => {
			await queryClient.cancelQueries({ queryKey });

			const previousData = queryClient.getQueryData<BusinessLine[]>(queryKey);

			queryClient.setQueryData<BusinessLine[]>(queryKey, (old) =>
				create(old || [], (draft) => {
					draft.push({
						id: "temp-id",
						name: newBusinessLine.name,
						code: newBusinessLine.code,
						state: newBusinessLine.state,
						parentId: newBusinessLine.parentId || null,
						createdAt: new Date().toISOString(),
						updatedAt: new Date().toISOString(),
						deletedAt: null,
					});
				}),
			);

			return { previousData };
		},
		onError: (_error, _variables, context) => {
			if (context?.previousData) {
				queryClient.setQueryData(queryKey, context.previousData);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
			queryClient.invalidateQueries({
				queryKey: businessLineOptions(service).queryKey,
			});
		},
	});
}
