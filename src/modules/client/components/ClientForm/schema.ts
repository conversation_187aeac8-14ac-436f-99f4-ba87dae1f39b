import * as v from "valibot";

export const clientFormSchema = v.object({
	name: v.pipe(v.string(), v.minLength(1, "El nombre es requerido")),
	fatherName: v.optional(v.string()),
	motherName: v.optional(v.string()),
	clientType: v.pipe(
		v.string(),
		v.minLength(1, "El tipo de cliente es requerido"),
		v.picklist(["natural", "juridica"], "Tipo de cliente inválido"),
	),
	documentType: v.pipe(
		v.string(),
		v.minLength(1, "El tipo de documento es requerido"),
		v.picklist(["dni", "ruc"], "Tipo de documento inválido"),
	),
	document: v.pipe(v.string(), v.minLength(1, "El documento es requerido")),
	ubication: v.optional(v.string()),
	socialReason: v.optional(v.string()),
	commercialName: v.optional(v.string()),
	condition: v.optional(v.string()),
	state: v.optional(v.string()),
	hasRetentionRegime: v.optional(v.boolean()),
	businessLineId: v.optional(v.string()),
	subBusinessLineId: v.pipe(
		v.string("Debe seleccionar un giro de negocio"),
		v.minLength(1, "El giro de negocio es requerido"),
	),
	channelId: v.optional(v.string()),
	contactName: v.optional(v.string()),
	email: v.optional(v.pipe(v.string(), v.email("Email inválido"))),
	phone: v.optional(v.string()),
});

export type ClientFormData = v.InferInput<typeof clientFormSchema>;
