import { Link } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import ClientTable from "../ClientTable";

export default function ClientManagement() {
	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Gestión de Clientes</h1>
				<Link to="/admin/clients/create" className="btn btn-primary">
					<Plus size={20} />
					Nuevo Cliente
				</Link>
			</div>
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<ClientTable />
				</div>
			</div>
		</div>
	);
}
