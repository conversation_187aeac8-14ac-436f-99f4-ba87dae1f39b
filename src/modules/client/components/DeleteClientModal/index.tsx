import { useEffect } from "react";
import type { Client } from "../../service/model/client";
import useDeleteClient from "../../hooks/use-delete-client";

interface DeleteClientModalProps {
	isOpen: boolean;
	setIsOpen: (isOpen: boolean) => void;
	client: Client;
}

export default function DeleteClientModal({
	isOpen,
	setIsOpen,
	client,
}: DeleteClientModalProps) {
	const deleteClientMutation = useDeleteClient();

	const handleDelete = () => {
		deleteClientMutation.mutate(client.id, {
			onSuccess: () => {
				setIsOpen(false);
			},
		});
	};

	useEffect(() => {
		const modal = document.getElementById("delete-client-modal") as HTMLDialogElement;
		if (isOpen) {
			modal?.showModal();
		} else {
			modal?.close();
		}
	}, [isOpen]);

	return (
		<dialog id="delete-client-modal" className="modal">
			<div className="modal-box">
				<h3 className="font-bold text-lg">Eliminar Cliente</h3>
				<p className="py-4">
					¿Está seguro que desea eliminar el cliente{" "}
					<strong>{client.name}</strong>?
				</p>
				<p className="text-sm text-gray-500">
					Esta acción no se puede deshacer.
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn"
						onClick={() => setIsOpen(false)}
						disabled={deleteClientMutation.isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={deleteClientMutation.isPending}
					>
						{deleteClientMutation.isPending && (
							<span className="loading loading-spinner loading-sm" />
						)}
						Eliminar
					</button>
				</div>
			</div>
			<form method="dialog" className="modal-backdrop">
				<button type="button" onClick={() => setIsOpen(false)}>
					close
				</button>
			</form>
		</dialog>
	);
}
