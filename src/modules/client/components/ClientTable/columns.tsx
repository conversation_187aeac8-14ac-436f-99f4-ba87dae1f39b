import { Link } from "@tanstack/react-router";
import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import type { Client } from "../../service/model/client";

const columnHelper = createColumnHelper<Client>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: ({ getValue }) => getValue(),
	}),
	columnHelper.accessor("document", {
		header: "Documento",
		cell: ({ getValue }) => getValue(),
	}),
	columnHelper.accessor("documentType", {
		header: "Tipo Documento",
		cell: ({ getValue }) => {
			const value = getValue();
			return value === "dni" ? "DNI" : value === "ruc" ? "RUC" : value;
		},
	}),
	columnHelper.accessor("clientType", {
		header: "Tipo Cliente",
		cell: ({ getValue }) => {
			const value = getValue();
			return value === "natural"
				? "Natural"
				: value === "juridica"
					? "Jurídica"
					: value;
		},
	}),
	columnHelper.accessor("email", {
		header: "Email",
		cell: ({ getValue }) => getValue() || "-",
	}),
	columnHelper.accessor("phone", {
		header: "Teléfono",
		cell: ({ getValue }) => getValue() || "-",
	}),
	columnHelper.accessor("state", {
		header: "Estado",
		cell: ({ getValue }) => getValue() || "-",
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const client = row.original;

			return (
				<div className="flex gap-2">
					<Link
						to="/admin/clients/$clientId/edit"
						params={{ clientId: client.id }}
						className="btn btn-sm btn-primary"
					>
						<Edit size={16} />
					</Link>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => {
							// TODO: Implement delete functionality
							console.log("Delete client:", client.id);
						}}
					>
						<Trash size={16} />
					</button>
				</div>
			);
		},
	}),
];
