import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { clientOptions } from "./client-options";

export default function useDeleteClient() {
	const service = useService();
	const { client } = service;
	const queryClient = useQueryClient();
	const queryKey = clientOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-client"],
		mutationFn: (id: string) => AppRuntime.runPromise(client.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousClients = queryClient.getQueryData(queryKey);

			if (previousClients) {
				queryClient.setQueryData(
					queryKey,
					create(previousClients, (draft) => {
						const index = draft.findIndex((c) => c.id === id);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousClients };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousClients);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
