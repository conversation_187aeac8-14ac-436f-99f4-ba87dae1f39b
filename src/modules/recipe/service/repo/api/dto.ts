import { Schema } from "effect";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Reci<PERSON>, UpdateRecipe } from "../../model/recipe";

export const ProductApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	image_url: Schema.NullOr(Schema.String),
	commercial_name: Schema.String,
	code: Schema.String,
	sku_code: Schema.String,
	measurement_unit_id: Schema.String,
	category_ids: Schema.mutable(Schema.Array(Schema.String)),
	brand_id: Schema.String,
	state: Schema.String,
	description: Schema.NullOr(Schema.String),
	can_be_sold: Schema.Boolean,
	can_be_purchased: Schema.Boolean,
	cost_price: Schema.NullOr(Schema.Number),
	cost_price_total: Schema.NullOr(Schema.Number),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const RecipeComponentApi = Schema.Struct({
	product: ProductApi,
	product_category_id: Schema.String,
	quantity: Schema.Number,
});

export const RecipeApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	batch_size: Schema.Number,
	measurement_unit_id: Schema.NullOr(Schema.String),
	products: Schema.mutable(Schema.Array(ProductApi)),
	components: Schema.mutable(Schema.Array(RecipeComponentApi)),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const RecipeFromApi = Schema.transform(RecipeApi, Recipe, {
	strict: true,
	decode: (recipeApi) => ({
		...recipeApi,
		batchSize: recipeApi.batch_size,
		measurementUnitID: recipeApi.measurement_unit_id,
		products: recipeApi.products.map((product) => ({
			...product,
			imageURL: product.image_url,
			commercialName: product.commercial_name,
			skuCode: product.sku_code,
			measurementUnitID: product.measurement_unit_id,
			categoryIDs: product.category_ids,
			brandID: product.brand_id,
			canBeSold: product.can_be_sold,
			canBePurchased: product.can_be_purchased,
			costPrice: product.cost_price,
			costPriceTotal: product.cost_price_total,
			createdAt: product.created_at,
			updatedAt: product.updated_at,
			deletedAt: product.deleted_at,
		})),
		components: recipeApi.components.map((component) => ({
			product: {
				...component.product,
				imageURL: component.product.image_url,
				commercialName: component.product.commercial_name,
				skuCode: component.product.sku_code,
				measurementUnitID: component.product.measurement_unit_id,
				categoryIDs: component.product.category_ids,
				brandID: component.product.brand_id,
				canBeSold: component.product.can_be_sold,
				canBePurchased: component.product.can_be_purchased,
				costPrice: component.product.cost_price,
				costPriceTotal: component.product.cost_price_total,
				createdAt: component.product.created_at,
				updatedAt: component.product.updated_at,
				deletedAt: component.product.deleted_at,
			},
			productCategoryID: component.product_category_id,
			quantity: component.quantity,
		})),
		createdAt: recipeApi.created_at,
		updatedAt: recipeApi.updated_at,
		deletedAt: recipeApi.deleted_at,
	}),
	encode: (recipe) => ({
		...recipe,
		batch_size: recipe.batchSize,
		measurement_unit_id: recipe.measurementUnitID,
		products: recipe.products.map((product) => ({
			...product,
			image_url: product.imageURL,
			commercial_name: product.commercialName,
			sku_code: product.skuCode,
			measurement_unit_id: product.measurementUnitID,
			category_ids: product.categoryIDs,
			brand_id: product.brandID,
			can_be_sold: product.canBeSold,
			can_be_purchased: product.canBePurchased,
			cost_price: product.costPrice,
			cost_price_total: product.costPriceTotal,
			created_at: product.createdAt,
			updated_at: product.updatedAt,
			deleted_at: product.deletedAt,
		})),
		components: recipe.components.map((component) => ({
			product: {
				...component.product,
				image_url: component.product.imageURL,
				commercial_name: component.product.commercialName,
				sku_code: component.product.skuCode,
				measurement_unit_id: component.product.measurementUnitID,
				category_ids: component.product.categoryIDs,
				brand_id: component.product.brandID,
				can_be_sold: component.product.canBeSold,
				can_be_purchased: component.product.canBePurchased,
				cost_price: component.product.costPrice,
				cost_price_total: component.product.costPriceTotal,
				created_at: component.product.createdAt,
				updated_at: component.product.updatedAt,
				deleted_at: component.product.deletedAt,
			},
			product_category_id: component.productCategoryID,
			quantity: component.quantity,
		})),
		created_at: recipe.createdAt,
		updated_at: recipe.updatedAt,
		deleted_at: recipe.deletedAt,
	}),
});

export const RecipeListFromApi = Schema.Array(RecipeFromApi);

export const CreateRecipeApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	batch_size: Schema.Number,
	measurement_unit_id: Schema.String,
	product_ids: Schema.mutable(Schema.Array(Schema.String)),
	components: Schema.mutable(
		Schema.Array(
			Schema.Struct({
				product_id: Schema.String,
				quantity: Schema.Number,
			}),
		),
	),
});

export const CreateRecipeApiFromCreateRecipe = Schema.transform(
	CreateRecipe,
	CreateRecipeApi,
	{
		strict: true,
		decode: (createRecipe) => ({
			...createRecipe,
			batch_size: createRecipe.batchSize,
			measurement_unit_id: createRecipe.measurementUnitID,
			product_ids: createRecipe.productIDs,
			components: createRecipe.components.map((component) => ({
				product_id: component.productID,
				quantity: component.quantity,
			})),
		}),
		encode: (createRecipeApi) => ({
			...createRecipeApi,
			batchSize: createRecipeApi.batch_size,
			measurementUnitID: createRecipeApi.measurement_unit_id,
			productIDs: createRecipeApi.product_ids,
			components: createRecipeApi.components.map((component) => ({
				productID: component.product_id,
				quantity: component.quantity,
			})),
		}),
	},
);

export const UpdateRecipeApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	batch_size: Schema.Number,
	measurement_unit_id: Schema.String,
	product_ids: Schema.mutable(Schema.Array(Schema.String)),
	components: Schema.mutable(
		Schema.Array(
			Schema.Struct({
				product_id: Schema.String,
				quantity: Schema.Number,
			}),
		),
	),
});

export const UpdateRecipeApiFromUpdateRecipe = Schema.transform(
	UpdateRecipe,
	UpdateRecipeApi,
	{
		strict: true,
		decode: (updateRecipe) => ({
			...updateRecipe,
			batch_size: updateRecipe.batchSize,
			measurement_unit_id: updateRecipe.measurementUnitID,
			product_ids: updateRecipe.productIDs,
			components: updateRecipe.components.map((component) => ({
				product_id: component.productID,
				quantity: component.quantity,
			})),
		}),
		encode: (updateRecipeApi) => ({
			...updateRecipeApi,
			batchSize: updateRecipeApi.batch_size,
			measurementUnitID: updateRecipeApi.measurement_unit_id,
			productIDs: updateRecipeApi.product_ids,
			components: updateRecipeApi.components.map((component) => ({
				productID: component.product_id,
				quantity: component.quantity,
			})),
		}),
	},
);

export const CreateRecipeApiResponse = Schema.Struct({
	id: Schema.String,
});
