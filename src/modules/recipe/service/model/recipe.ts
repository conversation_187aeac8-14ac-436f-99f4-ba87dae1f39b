import { Schema } from "effect";

export const RecipeComponent = Schema.Struct({
	product: Schema.Struct({
		id: Schema.String,
		name: Schema.String,
		imageURL: Schema.NullOr(Schema.String),
		commercialName: Schema.String,
		code: Schema.String,
		skuCode: Schema.String,
		measurementUnitID: Schema.String,
		categoryIDs: Schema.mutable(Schema.Array(Schema.String)),
		brandID: Schema.String,
		state: Schema.String,
		description: Schema.NullOr(Schema.String),
		canBeSold: Schema.Boolean,
		canBePurchased: Schema.Boolean,
		costPrice: Schema.NullOr(Schema.Number),
		costPriceTotal: Schema.NullOr(Schema.Number),
		createdAt: Schema.NullOr(Schema.String),
		updatedAt: Schema.NullOr(Schema.String),
		deletedAt: Schema.NullOr(Schema.String),
	}),
	productCategoryID: Schema.String,
	quantity: Schema.Number,
});
export type RecipeComponent = typeof RecipeComponent.Type;

export const Recipe = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	batchSize: Schema.Number,
	measurementUnitID: Schema.NullOr(Schema.String),
	products: Schema.mutable(
		Schema.Array(
			Schema.Struct({
				id: Schema.String,
				name: Schema.String,
				imageURL: Schema.NullOr(Schema.String),
				commercialName: Schema.String,
				code: Schema.String,
				skuCode: Schema.String,
				measurementUnitID: Schema.String,
				categoryIDs: Schema.mutable(Schema.Array(Schema.String)),
				brandID: Schema.String,
				state: Schema.String,
				description: Schema.NullOr(Schema.String),
				canBeSold: Schema.Boolean,
				canBePurchased: Schema.Boolean,
				costPrice: Schema.NullOr(Schema.Number),
				costPriceTotal: Schema.NullOr(Schema.Number),
				createdAt: Schema.NullOr(Schema.String),
				updatedAt: Schema.NullOr(Schema.String),
				deletedAt: Schema.NullOr(Schema.String),
			}),
		),
	),
	components: Schema.mutable(Schema.Array(RecipeComponent)),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Recipe = typeof Recipe.Type;

export const RecipeComponentCreate = Schema.Struct({
	productID: Schema.String,
	quantity: Schema.Number,
});
export type RecipeComponentCreate = typeof RecipeComponentCreate.Type;

export const CreateRecipe = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	batchSize: Schema.Number,
	measurementUnitID: Schema.String,
	productIDs: Schema.mutable(Schema.Array(Schema.String)),
	components: Schema.mutable(Schema.Array(RecipeComponentCreate)),
});
export type CreateRecipe = typeof CreateRecipe.Type;

export const UpdateRecipe = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	batchSize: Schema.Number,
	measurementUnitID: Schema.String,
	productIDs: Schema.mutable(Schema.Array(Schema.String)),
	components: Schema.mutable(Schema.Array(RecipeComponentCreate)),
});
export type UpdateRecipe = typeof UpdateRecipe.Type;
