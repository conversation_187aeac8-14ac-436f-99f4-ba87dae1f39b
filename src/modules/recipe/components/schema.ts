import * as v from "valibot";

export const CreateRecipeSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	type: v.pipe(
		v.string("Debe seleccionar un tipo"),
		v.minLength(1, "Debe seleccionar un tipo"),
	),
	batchSize: v.pipe(
		v.number("Debe ingresar un tamaño de lote"),
		v.minValue(1, "El tamaño de lote debe ser mayor a 0"),
	),
	measurementUnitID: v.pipe(
		v.string("Debe seleccionar una unidad de medida"),
		v.minLength(1, "Debe seleccionar una unidad de medida"),
	),
	productIDs: v.array(v.string()),
	components: v.array(
		v.object({
			productID: v.string("Debe seleccionar un producto"),
			quantity: v.pipe(
				v.number("Debe ingresar una cantidad"),
				v.minValue(0.01, "La cantidad debe ser mayor a 0"),
			),
		}),
	),
});
export type CreateRecipeSchema = v.InferOutput<typeof CreateRecipeSchema>;
