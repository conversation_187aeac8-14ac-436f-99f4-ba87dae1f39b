import { useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Trash2 } from "lucide-react";
import { useState } from "react";
import { useService } from "src/config/context/serviceProvider";
import { measurementUnitOptions } from "src/modules/measurement-unit/hooks/measurement-unit-options";
import type { Recipe } from "../../service/model/recipe";
import DeleteRecipeModal from "../DeleteRecipeModal";

export const columns: ColumnDef<Recipe>[] = [
	{
		accessorKey: "name",
		header: "Nombre",
	},
	{
		accessorKey: "code",
		header: "Código",
	},
	{
		accessorKey: "type",
		header: "Tipo",
		cell: ({ row }) => {
			const type = row.getValue("type") as string;
			return (
				<span
					className={`badge ${type === "bulk" ? "badge-primary" : "badge-secondary"}`}
				>
					{type === "bulk" ? "A Granel" : "Por Unidad"}
				</span>
			);
		},
	},
	{
		accessorKey: "batchSize",
		header: "Tamaño de Lote",
		cell: ({ row }) => {
			const batchSize = row.getValue("batchSize") as number;
			return <span>{batchSize}</span>;
		},
	},
	{
		accessorKey: "measurementUnitID",
		header: "Unidad de Medida",
		cell: ({ row }) => {
			const service = useService();
			const { data, isPending } = useQuery(measurementUnitOptions(service));

			if (isPending) {
				return <span className="loading loading-spinner" />;
			}

			const measurementUnitID = row.getValue("measurementUnitID") as string;
			return data?.find((unit) => unit.id === measurementUnitID)?.name || "-";
		},
	},
	{
		accessorKey: "components",
		header: "Componentes",
		cell: ({ row }) => {
			const components = row.getValue("components") as Recipe["components"];
			return <span>{components.length} componente(s)</span>;
		},
	},
	{
		accessorKey: "products",
		header: "Productos",
		cell: ({ row }) => {
			const products = row.getValue("products") as Recipe["products"];
			return <span>{products.length} producto(s)</span>;
		},
	},
	{
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const recipe = row.original;
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);

			return (
				<>
					<div className="flex gap-2">
						<Link
							to="/admin/manufacture/recipes/edit/$id"
							params={{ id: recipe.id }}
							className="btn btn-ghost btn-sm"
						>
							<Edit size={16} />
						</Link>
						<button
							type="button"
							className="btn btn-ghost btn-sm text-error"
							onClick={() => setIsDeleteOpen(true)}
						>
							<Trash2 size={16} />
						</button>
					</div>
					<DeleteRecipeModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						recipe={recipe}
					/>
				</>
			);
		},
	},
];
