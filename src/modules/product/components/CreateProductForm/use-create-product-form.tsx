import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useStore } from "@tanstack/react-store";
import { toast } from "react-toastify";
import { useService } from "src/config/context/serviceProvider";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import { categoryOptions } from "src/modules/category/hooks/category-options";
import { CategoryCode } from "~/category/service/model/category";
import useCreateProduct from "../../hooks/use-create-product";
import { productTabStore } from "../../store/createProductTab";
import { defaultValues } from "./defaultValues";
import { CreateProductSchema } from "./schema";

export default function useCreateProductForm() {
	const navigate = useNavigate();
	const service = useService();
	const { mutate, isPending } = useCreateProduct();

	const { data: categories = [] } = useQuery(categoryOptions(service));

	const productParentCategory = categories.find(
		(cat) => cat.code === CategoryCode.PRODUCTS,
	);

	const selectedTab = useStore(productTabStore);

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateProductSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					name: value.name,
					commercialName: value.commercialName,
					code: value.code,
					skuCode: value.skuCode,
					brandID: value.brandID,
					measurementUnitID: value.measurementUnitID,
					categoryIDs: productParentCategory?.id
						? [...value.categoryIDs, productParentCategory?.id]
						: value.categoryIDs,
					state: value.state,
					description: value.description || undefined,
					canBeSold: value.canBeSold,
					canBePurchased: value.canBePurchased,
					costPrice: value.costPrice,
					costPriceTotal: value.costPriceTotal,
					productionInfo: value.productionInfo || undefined,
				},
				{
					onSuccess: () => {
						toast.success("Producto creado exitosamente");
						navigate({ to: "/admin/products/products" });
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	return {
		form,
		isPending,
		selectedTab,
	};
}
