import * as v from "valibot";
import { ProductionInfoSchema } from "../../utils/productionInfoSchema";

export const CreateProductSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "El nombre es requerido"),
	),
	commercialName: v.pipe(
		v.string("Debe ingresar un nombre comercial"),
		v.minLength(1, "El nombre comercial es requerido"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "El código es requerido"),
	),
	skuCode: v.pipe(
		v.string("Debe ingresar un código SKU"),
		v.minLength(1, "El código SKU es requerido"),
	),
	brandID: v.pipe(v.string("Debe seleccionar una marca")),
	measurementUnitID: v.pipe(v.string("Debe seleccionar una unidad de medida")),
	categoryIDs: v.pipe(v.array(v.string("Debe seleccionar una categoría"))),
	state: v.pipe(
		v.string("Debe seleccionar un estado"),
		v.minLength(1, "El estado es requerido"),
	),
	description: v.optional(v.string()),
	canBeSold: v.boolean(),
	canBePurchased: v.boolean(),
	costPrice: v.optional(v.number()),
	costPriceTotal: v.optional(
		v.pipe(v.number(), v.minValue(0, "Debe ser mayor o igual a 0")),
	),
	productionInfo: v.optional(ProductionInfoSchema),
});

export type CreateProductSchema = v.InferOutput<typeof CreateProductSchema>;
