import { useQuery } from "@tanstack/react-query";
import { Building, DollarSign, Hash, Package, Tag } from "lucide-react";
import { useService } from "~/config/context/serviceProvider";
import { withForm } from "~/core/components/form/form";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { brandOptions } from "~/modules/brand/hooks/brand-options";
import {
	categoryOptions,
	categorySubcategoriesOptions,
} from "~/modules/category/hooks/category-options";
import { CategoryCode } from "~/modules/category/service/model/category";
import { measurementUnitOptions } from "~/modules/measurement-unit/hooks/measurement-unit-options";
import { defaultValues } from "../CreateProductForm/defaultValues";
import type { UpdateProductSchema } from "./schema";

const ProductInfo = withForm({
	defaultValues: defaultValues as UpdateProductSchema,
	render: ({ form }) => {
		const service = useService();

		const { data: brands = [] } = useQuery(brandOptions(service));
		const { data: measurementUnits = [] } = useQuery(
			measurementUnitOptions(service),
		);

		const { data: categories = [] } = useQuery(categoryOptions(service));

		const productParentCategory = categories.find(
			(cat) => cat.code === CategoryCode.PRODUCTS,
		);

		const { data: productSubcategories = [] } = useQuery(
			categorySubcategoriesOptions(service, productParentCategory?.id || ""),
		);

		return (
			<>
				<h2 className="card-title">Información del Producto</h2>
				<fieldset className="fieldset">
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="name"
							children={({ FSTextField }) => (
								<FSTextField
									label="Nombre"
									placeholder="Nombre del producto"
									prefixComponent={<Tag size={16} />}
								/>
							)}
						/>
						<form.AppField
							name="commercialName"
							children={({ FSTextField }) => (
								<FSTextField
									label="Nombre Comercial (Nombre, envase, cantidad, unidad de medida)"
									placeholder="Rehidratante Tropical pet x 500 ml"
									prefixComponent={<Building size={16} />}
								/>
							)}
						/>
					</div>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="code"
							children={({ FSTextField }) => (
								<FSTextField
									label="Código"
									placeholder="Código del producto"
									prefixComponent={<Hash size={16} />}
								/>
							)}
						/>
						<form.AppField
							name="skuCode"
							children={({ FSTextField }) => (
								<FSTextField
									label="Código SKU"
									placeholder="Código SKU del producto"
									prefixComponent={<Package size={16} />}
								/>
							)}
						/>
					</div>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="brandID"
							children={({ FSSelectField }) => (
								<FSSelectField
									label="Marca"
									placeholder="Seleccionar marca"
									options={brands.map((brand) => ({
										value: brand.id,
										label: brand.name,
									}))}
								/>
							)}
						/>
						<form.AppField
							name="measurementUnitID"
							children={({ FSSelectField }) => (
								<FSSelectField
									label="Unidad de Medida"
									placeholder="Seleccionar unidad de medida"
									options={measurementUnits.map((unit) => ({
										value: unit.id,
										label: unit.name,
									}))}
								/>
							)}
						/>
					</div>

					<form.AppField
						name="categoryIDs"
						children={({ FSComboBoxField }) => (
							<FSComboBoxField
								isMultiple
								label="Categoría"
								placeholder="Seleccionar categoría"
								options={productSubcategories.map((category) => ({
									value: category.id,
									label: category.name,
								}))}
							/>
						)}
					/>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
						<form.AppField
							name="costPrice"
							children={({ FSTextField }) => (
								<FSTextField
									label="Precio de Costo (Sugerencia)"
									placeholder="0.00"
									type="number"
									prefixComponent={<DollarSign size={16} />}
								/>
							)}
						/>
						<form.AppField
							name="costPriceTotal"
							children={({ FSTextField }) => (
								<FSTextField
									label="Total de Precio de Costo"
									placeholder="0"
									type="number"
									prefixComponent={<Hash size={16} />}
								/>
							)}
						/>
						<form.AppField
							name="state"
							children={({ FSSelectField }) => (
								<FSSelectField
									label="Estado"
									placeholder="Seleccionar estado"
									options={[
										{ value: "ACTIVE", label: "Activo" },
										{ value: "INACTIVE", label: "Inactivo" },
									]}
								/>
							)}
						/>
					</div>

					<form.AppField
						name="description"
						children={({ FSTextAreaField }) => (
							<FSTextAreaField
								label="Descripción"
								placeholder="Descripción del producto (opcional)"
							/>
						)}
					/>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<form.AppField
							name="canBeSold"
							children={({ FSToggleField }) => (
								<FSToggleField label="Se puede vender" />
							)}
						/>
						<form.AppField
							name="canBePurchased"
							children={({ FSToggleField }) => (
								<FSToggleField label="Se puede comprar" />
							)}
						/>
					</div>
				</fieldset>
			</>
		);
	},
});

export default ProductInfo;
