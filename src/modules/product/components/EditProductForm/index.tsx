import { Link } from "@tanstack/react-router";
import type { Product } from "../../service/model/product";
import { ProductTabType } from "../../store/createProductTab";
import ProductInfo from "./ProductInfo";
import ProductionInfo from "./ProductionInfo";
import useEditProductForm from "./use-edit-product-page-form";

interface EditProductFormProps {
	product: Product;
}

export default function EditProductForm({ product }: EditProductFormProps) {
	const { form, isPending, selectedTab } = useEditProductForm({ product });

	return (
		<div className="card bg-base-300 shadow-sm">
			<div className="card-body">
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						{selectedTab === ProductTabType.PRODUCT_INFO && (
							<ProductInfo form={form} />
						)}

						{selectedTab === ProductTabType.PRODUCTION_INFO && (
							<ProductionInfo form={form} />
						)}

						<div className="flex justify-end gap-4">
							<Link to="/admin/products/products" className="btn btn-ghost">
								Cancelar
							</Link>
							<form.SubscribeButton
								label="Actualizar Producto"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
