import { useStore } from "@tanstack/react-store";
import { cn } from "~/core/utils/classes";
import {
	ProductTabType,
	productTabStore,
	productTabStoreActions,
} from "../../store/createProductTab";

export default function CreateProductTabs() {
	const selectedTab = useStore(productTabStore);

	return (
		<div className="card bg-base-300">
			<div className="card-body">
				<div className="tabs tabs-box">
					<button
						type="button"
						className={cn(
							"tab w-1/2",
							selectedTab === ProductTabType.PRODUCT_INFO && "tab-active",
						)}
						onClick={() =>
							productTabStoreActions.setTab(ProductTabType.PRODUCT_INFO)
						}
					>
						Información del Producto
					</button>
					<button
						type="button"
						className={cn(
							"tab w-1/2",
							selectedTab === ProductTabType.PRODUCTION_INFO && "tab-active",
						)}
						onClick={() =>
							productTabStoreActions.setTab(ProductTabType.PRODUCTION_INFO)
						}
					>
						Información de Producción
					</button>
				</div>
			</div>
		</div>
	);
}
