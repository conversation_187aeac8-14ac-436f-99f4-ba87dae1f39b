import { Schema } from "effect";
import { CreateProduct, Product, UpdateProduct } from "../../model/product";
import { Material, ProductionInfo } from "../../model/productionInfo";

export const MaterialApi = Schema.Struct({
	product_id: Schema.String,
	quantity: Schema.Number,
});

export const MaterialFromApi = Schema.transform(MaterialApi, Material, {
	strict: true,
	decode: (materialApi) => ({
		...materialApi,
		productId: materialApi.product_id,
		quantity: materialApi.quantity,
	}),
	encode: (material) => ({
		...material,
		product_id: material.productId,
		quantity: material.quantity,
	}),
});

export const ProductionInfoApi = Schema.Struct({
	production_type: Schema.String,
	unit_quantity: Schema.NullOr(Schema.Number),
	measurement_unit_id: Schema.NullOr(Schema.String),
	materials: Schema.mutable(Schema.Array(MaterialFromApi)),
});

export const ProductionInfoFromApi = Schema.transform(
	ProductionInfoApi,
	ProductionInfo,
	{
		strict: true,
		decode: (productionInfoApi) => ({
			...productionInfoApi,
			productionType: productionInfoApi.production_type,
			unitQuantity: productionInfoApi.unit_quantity,
			measurementUnitID: productionInfoApi.measurement_unit_id,
			materials: productionInfoApi.materials,
		}),
		encode: (productionInfo) => ({
			...productionInfo,
			production_type: productionInfo.productionType,
			unit_quantity: productionInfo.unitQuantity,
			measurement_unit_id: productionInfo.measurementUnitID,
			materials: productionInfo.materials,
		}),
	},
);

export const ProductApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	image_url: Schema.NullOr(Schema.String),
	commercial_name: Schema.String,
	code: Schema.String,
	sku_code: Schema.String,
	measurement_unit_id: Schema.String,
	category_ids: Schema.mutable(Schema.Array(Schema.String)),
	brand_id: Schema.String,
	state: Schema.String,
	description: Schema.NullOr(Schema.String),
	can_be_sold: Schema.Boolean,
	can_be_purchased: Schema.Boolean,
	cost_price: Schema.NullOr(Schema.Number),
	cost_price_total: Schema.NullOr(Schema.Number),
	production_info: Schema.NullishOr(ProductionInfoFromApi),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ProductFromApi = Schema.transform(ProductApi, Product, {
	strict: true,
	decode: (productApi) => ({
		...productApi,
		imageURL: productApi.image_url,
		commercialName: productApi.commercial_name,
		skuCode: productApi.sku_code,
		measurementUnitID: productApi.measurement_unit_id,
		categoryIDs: productApi.category_ids,
		brandID: productApi.brand_id,
		canBeSold: productApi.can_be_sold,
		canBePurchased: productApi.can_be_purchased,
		costPrice: productApi.cost_price,
		costPriceTotal: productApi.cost_price_total,
		productionInfo: productApi.production_info,
		createdAt: productApi.created_at,
		updatedAt: productApi.updated_at,
		deletedAt: productApi.deleted_at,
	}),
	encode: (product) => ({
		...product,
		image_url: product.imageURL,
		commercial_name: product.commercialName,
		sku_code: product.skuCode,
		measurement_unit_id: product.measurementUnitID,
		category_ids: product.categoryIDs,
		brand_id: product.brandID,
		can_be_sold: product.canBeSold,
		can_be_purchased: product.canBePurchased,
		cost_price: product.costPrice,
		cost_price_total: product.costPriceTotal,
		production_info: product.productionInfo,
		created_at: product.createdAt,
		updated_at: product.updatedAt,
		deleted_at: product.deletedAt,
	}),
});

export const ProductListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ProductFromApi))),
	Schema.mutable(Schema.Array(Product)),
	{
		strict: true,
		decode: (productApiList) => (productApiList ? productApiList : []),
		encode: (productList) => productList,
	},
);

export const CreateProductApi = Schema.Struct({
	name: Schema.String,
	image_url: Schema.optional(Schema.String),
	commercial_name: Schema.String,
	code: Schema.String,
	sku_code: Schema.String,
	measurement_unit_id: Schema.String,
	category_ids: Schema.mutable(Schema.Array(Schema.String)),
	brand_id: Schema.String,
	state: Schema.String,
	description: Schema.optional(Schema.String),
	can_be_sold: Schema.Boolean,
	can_be_purchased: Schema.Boolean,
	cost_price: Schema.optional(Schema.Number),
	cost_price_total: Schema.optional(Schema.Number),
	production_info: Schema.optional(ProductionInfoFromApi),
});

export const CreateProductApiFromCreateProduct = Schema.transform(
	CreateProductApi,
	CreateProduct,
	{
		strict: true,
		decode: (createProductApi) => ({
			...createProductApi,
			imageURL: createProductApi.image_url,
			commercialName: createProductApi.commercial_name,
			skuCode: createProductApi.sku_code,
			measurementUnitID: createProductApi.measurement_unit_id,
			categoryIDs: createProductApi.category_ids,
			brandID: createProductApi.brand_id,
			canBeSold: createProductApi.can_be_sold,
			canBePurchased: createProductApi.can_be_purchased,
			costPrice: createProductApi.cost_price,
			costPriceTotal: createProductApi.cost_price_total,
			productionInfo: createProductApi.production_info,
		}),
		encode: (createProduct) => ({
			...createProduct,
			image_url: createProduct.imageURL,
			commercial_name: createProduct.commercialName,
			sku_code: createProduct.skuCode,
			measurement_unit_id: createProduct.measurementUnitID,
			category_ids: createProduct.categoryIDs,
			brand_id: createProduct.brandID,
			can_be_sold: createProduct.canBeSold,
			can_be_purchased: createProduct.canBePurchased,
			cost_price: createProduct.costPrice,
			cost_price_total: createProduct.costPriceTotal,
			production_info: createProduct.productionInfo,
		}),
	},
);

export const UpdateProductApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	image_url: Schema.optional(Schema.String),
	commercial_name: Schema.String,
	code: Schema.String,
	sku_code: Schema.String,
	measurement_unit_id: Schema.String,
	category_ids: Schema.mutable(Schema.Array(Schema.String)),
	brand_id: Schema.String,
	state: Schema.String,
	description: Schema.optional(Schema.String),
	can_be_sold: Schema.Boolean,
	can_be_purchased: Schema.Boolean,
	cost_price: Schema.optional(Schema.Number),
	cost_price_total: Schema.optional(Schema.Number),
	production_info: Schema.optional(ProductionInfoFromApi),
});

export const UpdateProductApiFromUpdateProduct = Schema.transform(
	UpdateProductApi,
	UpdateProduct,
	{
		strict: true,
		decode: (updateProductApi) => ({
			...updateProductApi,
			imageURL: updateProductApi.image_url,
			commercialName: updateProductApi.commercial_name,
			skuCode: updateProductApi.sku_code,
			measurementUnitID: updateProductApi.measurement_unit_id,
			categoryIDs: updateProductApi.category_ids,
			brandID: updateProductApi.brand_id,
			canBeSold: updateProductApi.can_be_sold,
			canBePurchased: updateProductApi.can_be_purchased,
			costPrice: updateProductApi.cost_price,
			costPriceTotal: updateProductApi.cost_price_total,
			productionInfo: updateProductApi.production_info,
		}),
		encode: (updateProduct) => ({
			...updateProduct,
			image_url: updateProduct.imageURL,
			commercial_name: updateProduct.commercialName,
			sku_code: updateProduct.skuCode,
			measurement_unit_id: updateProduct.measurementUnitID,
			category_ids: updateProduct.categoryIDs,
			brand_id: updateProduct.brandID,
			can_be_sold: updateProduct.canBeSold,
			can_be_purchased: updateProduct.canBePurchased,
			cost_price: updateProduct.costPrice,
			cost_price_total: updateProduct.costPriceTotal,
			production_info: updateProduct.productionInfo,
		}),
	},
);

export const CreateProductApiResponse = Schema.String;
