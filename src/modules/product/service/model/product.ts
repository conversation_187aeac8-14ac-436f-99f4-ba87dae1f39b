import { Schema } from "effect";
import { ProductionInfo } from "./productionInfo";

export const Product = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	imageURL: Schema.NullOr(Schema.String),
	commercialName: Schema.String,
	code: Schema.String,
	skuCode: Schema.String,
	measurementUnitID: Schema.String,
	categoryIDs: Schema.mutable(Schema.Array(Schema.String)),
	brandID: Schema.String,
	state: Schema.String,
	description: Schema.NullOr(Schema.String),
	canBeSold: Schema.Boolean,
	canBePurchased: Schema.Boolean,
	costPrice: Schema.NullOr(Schema.Number),
	costPriceTotal: Schema.NullOr(Schema.Number),
	productionInfo: Schema.NullishOr(ProductionInfo),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Product = typeof Product.Type;

export const CreateProduct = Schema.Struct({
	name: Schema.String,
	imageURL: Schema.optional(Schema.String),
	commercialName: Schema.String,
	code: Schema.String,
	skuCode: Schema.String,
	measurementUnitID: Schema.String,
	categoryIDs: Schema.mutable(Schema.Array(Schema.String)),
	brandID: Schema.String,
	state: Schema.String,
	description: Schema.optional(Schema.String),
	canBeSold: Schema.Boolean,
	canBePurchased: Schema.Boolean,
	costPrice: Schema.optional(Schema.Number),
	costPriceTotal: Schema.optional(Schema.Number),
	productionInfo: Schema.optional(ProductionInfo),
});
export type CreateProduct = typeof CreateProduct.Type;

export const UpdateProduct = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	imageURL: Schema.optional(Schema.String),
	commercialName: Schema.String,
	code: Schema.String,
	skuCode: Schema.String,
	measurementUnitID: Schema.String,
	categoryIDs: Schema.mutable(Schema.Array(Schema.String)),
	brandID: Schema.String,
	state: Schema.String,
	description: Schema.optional(Schema.String),
	canBeSold: Schema.Boolean,
	canBePurchased: Schema.Boolean,
	costPrice: Schema.optional(Schema.Number),
	costPriceTotal: Schema.optional(Schema.Number),
	productionInfo: Schema.optional(ProductionInfo),
});
export type UpdateProduct = typeof UpdateProduct.Type;
