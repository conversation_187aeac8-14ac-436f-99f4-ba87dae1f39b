import { Schema } from "effect";

export const Material = Schema.Struct({
	productId: Schema.String,
	quantity: Schema.Number,
});
export type Material = typeof Material.Type;

export const ProductionInfo = Schema.Struct({
	productionType: Schema.String,
	unitQuantity: Schema.NullOr(Schema.Number),
	measurementUnitID: Schema.NullOr(Schema.String),
	materials: Schema.mutable(Schema.Array(Material)),
});
export type ProductionInfo = typeof ProductionInfo.Type;
