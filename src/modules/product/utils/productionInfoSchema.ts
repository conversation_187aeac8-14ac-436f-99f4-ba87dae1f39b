import * as v from "valibot";

export const MaterialSchema = v.object({
	productId: v.pipe(
		v.string("Debe ingresar un producto"),
		v.minLength(1, "El producto es requerido"),
	),
	quantity: v.pipe(v.number(), v.minValue(0, "Debe ser mayor o igual a 0")),
});

export const ProductionInfoSchema = v.object({
	productionType: v.pipe(
		v.string("Debe seleccionar un tipo de producción"),
		v.minLength(1, "El tipo de producción es requerido"),
	),
	unitQuantity: v.nullable(
		v.pipe(v.number(), v.minValue(0, "Debe ser mayor o igual a 0")),
	),
	measurementUnitID: v.nullable(
		v.pipe(
			v.string("Debe seleccionar una unidad de medida"),
			v.minLength(1, "La unidad de medida es requerida"),
		),
	),
	materials: v.array(MaterialSchema),
});
