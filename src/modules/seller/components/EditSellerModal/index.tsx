import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import TextModal from "src/core/components/TextModal";
import { getErrorResult } from "src/core/utils/effectErrors";
import { sellerOptionsById } from "../../hooks/seller-options";
import EditSellerForm from "./EditSellerForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditSellerModal({ isOpen, setIsOpen, id }: Props) {
	const svc = useService();
	const { data, isError, error, isPending } = useQuery({
		...sellerOptionsById(svc, id),
		enabled: isOpen,
	});

	useEffect(() => {
		if (error) {
			console.log(error);
		}
	}, [error]);

	if (isPending) return <TextModal open={isOpen} text="Cargando..." />;

	if (isError)
		return (
			<TextModal
				open={isOpen}
				text="No se pudo cargar el vendedor"
				title={getErrorResult(error).error.code.toString()}
			/>
		);

	return <EditSellerForm isOpen={isOpen} setIsOpen={setIsOpen} seller={data} />;
}
