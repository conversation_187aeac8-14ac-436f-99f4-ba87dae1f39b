import { FileText, IdCard, User, Users } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { cn } from "src/core/utils/classes";
import useEditSellerModal, {
	type EditSellerModalProps,
} from "./use-edit-seller-modal";

export default function EditSellerForm({
	isOpen,
	setIsOpen,
	seller,
}: EditSellerModalProps) {
	const { seller: sellerService } = useService();
	const { form, handleClose } = useEditSellerModal({
		isOpen,
		setIsOpen,
		seller,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Vendedor</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre del vendedor"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="fatherName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Paterno"
										placeholder="Apellido paterno"
										prefixComponent={<Users size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="motherName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Materno"
										placeholder="Apellido materno"
										prefixComponent={<Users size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="identityDocumentNumber"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (
											!value ||
											value.trim() === "" ||
											value === seller.identityDocumentNumber
										) {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(
												sellerService.validateIdentityDocument(value),
											);
											return undefined;
										} catch (e) {
											return [
												{ message: "El documento de identidad ya existe" },
											];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Documento de Identidad"
										placeholder="Número de documento"
										prefixComponent={<IdCard size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="state"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Estado"
										placeholder="Seleccionar estado"
										options={[
											{ value: "ACTIVE", label: "Activo" },
											{ value: "INACTIVE", label: "Inactivo" },
										]}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<button type="submit" className="btn btn-primary">
								Actualizar
							</button>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
