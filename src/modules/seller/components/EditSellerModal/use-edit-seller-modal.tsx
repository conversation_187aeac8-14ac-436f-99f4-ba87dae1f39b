import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useUpdateSeller from "../../hooks/use-update-seller";
import type { Seller } from "../../service/model/seller";
import { CreateSellerSchema } from "../CreateSellerModal/schema";

export interface EditSellerModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	seller: Seller;
}

export default function useEditSellerModal({
	setIsOpen,
	seller,
}: EditSellerModalProps) {
	const { mutate } = useUpdateSeller();

	const form = useAppForm({
		defaultValues: {
			name: seller.name,
			fatherName: seller.fatherName,
			motherName: seller.motherName,
			identityDocumentNumber: seller.identityDocumentNumber,
			state: seller.state,
		} as CreateSellerSchema,
		validators: {
			onChange: CreateSellerSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: seller.id,
					name: value.name,
					fatherName: value.fatherName,
					motherName: value.motherName,
					identityDocumentNumber: value.identityDocumentNumber,
					state: value.state,
				},
				{
					onSuccess: () => {
						toast.success("Vendedor actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
