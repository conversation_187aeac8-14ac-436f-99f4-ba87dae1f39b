import { toast } from "react-toastify";
import CloseModal from "src/core/components/CloseModal";
import { cn } from "src/core/utils/classes";
import { getErrorResult } from "src/core/utils/effectErrors";
import useDeleteSeller from "../../hooks/use-delete-seller";
import type { Seller } from "../../service/model/seller";

export interface DeleteSellerModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	seller: Seller;
}

export default function DeleteSellerModal({
	isOpen,
	setIsOpen,
	seller,
}: DeleteSellerModalProps) {
	const { mutate, isPending } = useDeleteSeller();

	const handleDelete = () => {
		mutate(seller.id, {
			onSuccess: () => {
				toast.success("Vendedor eliminado exitosamente");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	};

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar Vendedor</h3>
				<p className="py-4">
					¿Estás seguro de que deseas eliminar al vendedor{" "}
					<strong>
						{seller.name} {seller.fatherName} {seller.motherName}
					</strong>
					?
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? "Eliminando..." : "Eliminar"}
					</button>
				</div>
			</div>
		</div>
	);
}
