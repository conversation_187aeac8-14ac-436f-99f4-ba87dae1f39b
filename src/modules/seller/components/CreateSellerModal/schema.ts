import * as v from "valibot";

export const CreateSellerSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "El nombre es requerido"),
	),
	fatherName: v.pipe(
		v.string("Debe ingresar el apellido paterno"),
		v.minLength(1, "El apellido paterno es requerido"),
	),
	motherName: v.pipe(
		v.string("Debe ingresar el apellido materno"),
		v.min<PERSON>ength(1, "El apellido materno es requerido"),
	),
	identityDocumentNumber: v.pipe(
		v.string("Debe ingresar el número de documento de identidad"),
		v.minLength(1, "El número de documento de identidad es requerido"),
	),
	state: v.pipe(
		v.string("Debe seleccionar un estado"),
		v.minLength(1, "El estado es requerido"),
	),
});
export type CreateSellerSchema = v.InferOutput<typeof CreateSellerSchema>;
