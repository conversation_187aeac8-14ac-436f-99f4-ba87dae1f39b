import { User, Users, IdCard, FileText } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { cn } from "src/core/utils/classes";
import type { CreateSellerModalProps } from "./use-create-modal";
import useCreateSellerModal from "./use-create-modal";

export default function CreateSellerModal({
	isOpen,
	setIsOpen,
}: CreateSellerModalProps) {
	const { seller } = useService();
	const { form, handleClose, isPending } = useCreateSellerModal({
		isOpen,
		setIsOpen,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Crear Vendedor</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre del vendedor"
										prefixComponent={<User size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="fatherName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Paterno"
										placeholder="Apellido paterno"
										prefixComponent={<Users size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="motherName"
								children={({ FSTextField }) => (
									<FSTextField
										label="Apellido Materno"
										placeholder="Apellido materno"
										prefixComponent={<Users size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="identityDocumentNumber"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(seller.validateIdentityDocument(value));
											return undefined;
										} catch (e) {
											return [{ message: "El documento de identidad ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Documento de Identidad"
										placeholder="Número de documento"
										prefixComponent={<IdCard size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="state"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Estado"
										placeholder="Seleccionar estado"
										prefixComponent={<FileText size={16} />}
										options={[
											{ value: "ACTIVE", label: "Activo" },
											{ value: "INACTIVE", label: "Inactivo" },
										]}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
