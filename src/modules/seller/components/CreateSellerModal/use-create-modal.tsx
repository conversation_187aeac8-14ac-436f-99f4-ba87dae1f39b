import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useCreateSeller from "../../hooks/use-create-seller";
import { CreateSellerSchema } from "./schema";

export interface CreateSellerModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function useCreateSellerModal({
	setIsOpen,
}: CreateSellerModalProps) {
	const { mutate, isPending } = useCreateSeller();

	const form = useAppForm({
		defaultValues: {
			name: "",
			fatherName: "",
			motherName: "",
			identityDocumentNumber: "",
			state: "ACTIVE",
		} as CreateSellerSchema,
		validators: {
			onChange: CreateSellerSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onSuccess: () => {
					toast.success("Vendedor creado exitosamente");
					handleClose();
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
