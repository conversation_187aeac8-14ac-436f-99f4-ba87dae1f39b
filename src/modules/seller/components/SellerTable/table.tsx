import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "src/core/components/tables/BasicTable";
import type { Seller } from "../../service/model/seller";
import { columns } from "./columns";

interface Props {
	sellers: Seller[];
}

export default function Table({ sellers }: Props) {
	const table = useReactTable({
		data: sellers,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
