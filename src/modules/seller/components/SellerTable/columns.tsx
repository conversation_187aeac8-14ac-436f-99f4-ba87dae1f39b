import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import type { Seller } from "../../service/model/seller";
import DeleteSellerModal from "../DeleteSellerModal";
import EditSellerModal from "../EditSellerModal";

const columnHelper = createColumnHelper<Seller>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("fatherName", {
		header: "Apellido Paterno",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("motherName", {
		header: "Apellido Materno",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("identityDocumentNumber", {
		header: "Documento de Identidad",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("state", {
		header: "Estado",
		cell: (info) => info.getValue(),
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const seller = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditSellerModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={seller.id}
					/>
					<DeleteSellerModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						seller={seller}
					/>
				</div>
			);
		},
	}),
];
