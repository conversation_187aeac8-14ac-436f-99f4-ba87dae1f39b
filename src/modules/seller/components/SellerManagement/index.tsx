import { Plus } from "lucide-react";
import { useState } from "react";
import SellerTable from "../SellerTable";
import CreateSellerModal from "../CreateSellerModal";

export default function SellerManagement() {
	const [isCreateOpen, setIsCreateOpen] = useState(false);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Gestión de Vendedores</h1>
				<button
					type="button"
					className="btn btn-primary"
					onClick={() => setIsCreateOpen(true)}
				>
					<Plus size={16} />
					Nuevo Vendedor
				</button>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<SellerTable />
				</div>
			</div>

			<CreateSellerModal isOpen={isCreateOpen} setIsOpen={setIsCreateOpen} />
		</div>
	);
}
