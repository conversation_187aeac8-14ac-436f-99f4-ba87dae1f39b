import { Schema } from "effect";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Update<PERSON><PERSON> } from "../../model/seller";

export const SellerApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	father_name: Schema.String,
	mother_name: Schema.String,
	identity_document_number: Schema.String,
	state: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const SellerFromApi = Schema.transform(Seller<PERSON>pi, Seller, {
	strict: true,
	decode: (sellerApi) => ({
		...sellerApi,
		fatherName: sellerApi.father_name,
		motherName: sellerApi.mother_name,
		identityDocumentNumber: sellerApi.identity_document_number,
		createdAt: sellerApi.created_at,
		updatedAt: sellerApi.updated_at,
		deletedAt: sellerApi.deleted_at,
	}),
	encode: (seller) => ({
		...seller,
		father_name: seller.fatherName,
		mother_name: seller.motherName,
		identity_document_number: seller.identityDocumentNumber,
		created_at: seller.createdAt,
		updated_at: seller.updatedAt,
		deleted_at: seller.deletedAt,
	}),
});

export const SellerListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(SellerFromApi))),
	Schema.mutable(Schema.Array(Seller)),
	{
		strict: true,
		decode: (sellerApiList) => (sellerApiList ? sellerApiList : []),
		encode: (sellerList) => sellerList,
	},
);

export const CreateSellerApi = Schema.Struct({
	name: Schema.String,
	father_name: Schema.String,
	mother_name: Schema.String,
	identity_document_number: Schema.String,
	state: Schema.String,
});

export const CreateSellerApiFromCreateSeller = Schema.transform(
	CreateSeller,
	CreateSellerApi,
	{
		strict: true,
		decode: (createSeller) => ({
			...createSeller,
			father_name: createSeller.fatherName,
			mother_name: createSeller.motherName,
			identity_document_number: createSeller.identityDocumentNumber,
		}),
		encode: (createSellerApi) => ({
			...createSellerApi,
			fatherName: createSellerApi.father_name,
			motherName: createSellerApi.mother_name,
			identityDocumentNumber: createSellerApi.identity_document_number,
		}),
	},
);

export const UpdateSellerApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	father_name: Schema.String,
	mother_name: Schema.String,
	identity_document_number: Schema.String,
	state: Schema.String,
});

export const UpdateSellerApiFromUpdateSeller = Schema.transform(
	UpdateSeller,
	UpdateSellerApi,
	{
		strict: true,
		decode: (updateSeller) => ({
			...updateSeller,
			father_name: updateSeller.fatherName,
			mother_name: updateSeller.motherName,
			identity_document_number: updateSeller.identityDocumentNumber,
		}),
		encode: (updateSellerApi) => ({
			...updateSellerApi,
			fatherName: updateSellerApi.father_name,
			motherName: updateSellerApi.mother_name,
			identityDocumentNumber: updateSellerApi.identity_document_number,
		}),
	},
);

export const CreateSellerApiResponse = Schema.String;
