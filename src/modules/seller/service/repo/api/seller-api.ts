import { Http<PERSON><PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "src/core/service/repo/api";
import {
	handleDResponse,
	handleResponse,
} from "src/core/service/repo/api/utils";
import { SellerRepository } from "../../model/repository";
import type { CreateSeller, UpdateSeller } from "../../model/seller";
import {
	CreateSellerApiFromCreateSeller,
	CreateSellerApiResponse,
	SellerFromApi,
	SellerListFromApi,
	UpdateSellerApiFromUpdateSeller,
} from "./dto";

const baseUrl = "/v1/sellers";

const makeSellerApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(SellerListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(SellerFromApi))),
		create: (seller: CreateSeller) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateSellerApiFromCreateSeller)(seller),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateSellerApiResponse))),
		update: (seller: UpdateSeller) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateSellerApiFromUpdateSeller)(seller),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateIdentityDocument: (identityDocument: string) =>
			httpClient
				.get(`${baseUrl}/validate/identity-document/${identityDocument}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const sellerApiRepoLive = Layer.effect(
	SellerRepository,
	makeSellerApiRepo,
);
