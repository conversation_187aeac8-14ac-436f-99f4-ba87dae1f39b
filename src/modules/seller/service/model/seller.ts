import { Schema } from "effect";

export const Seller = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	fatherName: Schema.String,
	motherName: Schema.String,
	identityDocumentNumber: Schema.String,
	state: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Seller = typeof Seller.Type;

export const CreateSeller = Schema.Struct({
	name: Schema.String,
	fatherName: Schema.String,
	motherName: Schema.String,
	identityDocumentNumber: Schema.String,
	state: Schema.String,
});
export type CreateSeller = typeof CreateSeller.Type;

export const UpdateSeller = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	fatherName: Schema.String,
	motherName: Schema.String,
	identityDocumentNumber: Schema.String,
	state: Schema.String,
});
export type UpdateSeller = typeof UpdateSeller.Type;
