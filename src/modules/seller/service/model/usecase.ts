import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { <PERSON><PERSON>, CreateSeller, UpdateSeller } from "./seller";

export class SellerUsecase extends Effect.Tag("SellerUsecase")<
	SellerUsecase,
	{
		readonly getAll: () => Effect.Effect<Seller[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Seller, AppError>;
		readonly create: (seller: CreateSeller) => Effect.Effect<string, AppError>;
		readonly update: (seller: UpdateSeller) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateIdentityDocument: (identityDocument: string) => Effect.Effect<void, AppError>;
	}
>() {}
