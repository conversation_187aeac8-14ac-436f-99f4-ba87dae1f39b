import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Seller, CreateSeller } from "../service/model/seller";
import { sellerOptions } from "./seller-options";

export default function useCreateSeller() {
	const service = useService();
	const { seller } = service;
	const queryClient = useQueryClient();
	const queryKey = sellerOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-seller"],
		mutationFn: (newSeller: CreateSeller) =>
			AppRuntime.runPromise(seller.create(newSeller)),
		onMutate: async (newSeller) => {
			await queryClient.cancelQueries({ queryKey });

			const previousSellers = queryClient.getQueryData(queryKey);

			if (previousSellers) {
				queryClient.setQueryData(
					queryKey,
					create(previousSellers, (draft) => {
						draft.push({
							id: "new",
							name: newSeller.name,
							fatherName: newSeller.fatherName,
							motherName: newSeller.motherName,
							identityDocumentNumber: newSeller.identityDocumentNumber,
							state: newSeller.state,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						} as Seller);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					{
						id: "new",
						name: newSeller.name,
						fatherName: newSeller.fatherName,
						motherName: newSeller.motherName,
						identityDocumentNumber: newSeller.identityDocumentNumber,
						state: newSeller.state,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					} as Seller,
				]);
			}

			return { previousSellers };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousSellers);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
