import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";

import { sellerOptions } from "./seller-options";

export default function useDeleteSeller() {
	const service = useService();
	const { seller } = service;
	const queryClient = useQueryClient();
	const queryKey = sellerOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-seller"],
		mutationFn: (id: string) => AppRuntime.runPromise(seller.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousSellers = queryClient.getQueryData(queryKey);

			if (previousSellers) {
				queryClient.setQueryData(
					queryKey,
					create(previousSellers, (draft) => {
						const index = draft.findIndex((s) => s.id === id);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousSellers };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousSellers);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
