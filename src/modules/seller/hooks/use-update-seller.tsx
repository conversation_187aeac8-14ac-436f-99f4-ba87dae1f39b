import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Seller, UpdateSeller } from "../service/model/seller";
import { sellerOptions } from "./seller-options";

export default function useUpdateSeller() {
	const service = useService();
	const { seller } = service;
	const queryClient = useQueryClient();
	const queryKey = sellerOptions(service).queryKey;

	return useMutation({
		mutationKey: ["update-seller"],
		mutationFn: (updateSeller: UpdateSeller) =>
			AppRuntime.runPromise(seller.update(updateSeller)),
		onMutate: async (updateSeller) => {
			await queryClient.cancelQueries({ queryKey });

			const previousSellers = queryClient.getQueryData(queryKey);

			if (previousSellers) {
				queryClient.setQueryData(
					queryKey,
					create(previousSellers, (draft) => {
						const index = draft.findIndex((s) => s.id === updateSeller.id);
						if (index !== -1) {
							draft[index] = {
								...draft[index],
								name: updateSeller.name,
								fatherName: updateSeller.fatherName,
								motherName: updateSeller.motherName,
								identityDocumentNumber: updateSeller.identityDocumentNumber,
								state: updateSeller.state,
								updatedAt: new Date().toISOString(),
							};
						}
					}),
				);
			}

			return { previousSellers };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousSellers);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
