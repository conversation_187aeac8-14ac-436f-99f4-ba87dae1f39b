import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const sellerOptions = ({ seller }: serviceRegistry) =>
	queryOptions({
		queryKey: ["sellers"],
		queryFn: () => AppRuntime.runPromise(seller.getAll()),
	});

export const sellerOptionsById = ({ seller }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["sellers", id],
		queryFn: () => AppRuntime.runPromise(seller.getById(id)),
	});
