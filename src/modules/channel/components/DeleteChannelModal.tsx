import { toast } from "react-toastify";
import CloseModal from "src/core/components/CloseModal";
import { cn } from "src/core/utils/classes";
import { getErrorResult } from "src/core/utils/effectErrors";
import useDeleteChannel from "../hooks/use-delete-channel";
import type { Channel } from "../service/model/channel";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	channel: Channel;
}

export default function DeleteChannelModal({ isOpen, setIsOpen, channel }: Props) {
	const { mutate, isPending } = useDeleteChannel();

	function handleDelete() {
		mutate(channel.id, {
			onSuccess: () => {
				toast.success("Canal eliminado");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	}

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar canal</h3>
				<p>¿Estás seguro de que quieres eliminar este canal?</p>
				<p className="mt-2 text-gray-600 text-sm">
					Canal: {channel.name} ({channel.state === "active" ? "Activo" : "Inactivo"})
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-primary"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending ? "Eliminando..." : "Eliminar"}
					</button>
				</div>
			</div>
		</div>
	);
}
