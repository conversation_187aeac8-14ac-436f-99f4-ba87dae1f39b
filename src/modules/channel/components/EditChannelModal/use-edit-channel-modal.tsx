import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useUpdateChannel from "../../hooks/use-update-channel";
import type { Channel } from "../../service/model/channel";
import { CreateChannelSchema } from "../schema";

export interface EditChannelModalProps {
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	channel: Channel;
}

export default function useEditChannelModal({
	setIsOpen,
	channel,
}: EditChannelModalProps) {
	const { mutate } = useUpdateChannel();

	const form = useAppForm({
		defaultValues: {
			name: channel.name,
			state: channel.state,
		} as CreateChannelSchema,
		validators: {
			onChange: CreateChannelSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: channel.id,
					name: value.name,
					state: value.state,
				},
				{
					onSuccess: () => {
						toast.success("Canal actualizado");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
