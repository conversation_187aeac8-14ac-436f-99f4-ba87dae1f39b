import { Hash, Tag } from "lucide-react";
import type { Channel } from "../../service/model/channel";
import useEditChannelModal from "./use-edit-channel-modal";

interface Props {
	channel: Channel;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function EditChannelForm({ channel, setIsOpen }: Props) {
	const { form, handleClose } = useEditChannelModal({
		setIsOpen,
		channel,
	});

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<form.AppField
						name="name"
						children={({ FSTextField }) => (
							<FSTextField
								label="Nombre"
								placeholder="Nombre del canal"
								prefixComponent={<Tag size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="state"
						children={({ FSSelectField }) => (
							<FSSelectField
								label="Estado"
								placeholder="Seleccionar estado"
								prefixComponent={<Hash size={16} />}
								options={[
									{ value: "active", label: "Activo" },
									{ value: "inactive", label: "Inactivo" },
								]}
							/>
						)}
					/>
				</fieldset>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-ghost"
						onClick={handleClose}
					>
						Cancelar
					</button>
					<form.SubscribeButton
						label="Actualizar"
						className="btn btn-primary"
					/>
				</div>
			</form.AppForm>
		</form>
	);
}
