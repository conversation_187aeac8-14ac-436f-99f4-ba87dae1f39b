import { Plus } from "lucide-react";
import { useState } from "react";
import ChannelTable from "../ChannelTable";
import CreateChannelModal from "../CreateChannelModal";

export default function ChannelManagement() {
	const [isCreateOpen, setIsCreateOpen] = useState(false);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Gestión de Canales</h1>
				<button
					type="button"
					className="btn btn-primary"
					onClick={() => setIsCreateOpen(true)}
				>
					<Plus size={16} />
					Nuevo Canal
				</button>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<ChannelTable />
				</div>
			</div>

			<CreateChannelModal isOpen={isCreateOpen} setIsOpen={setIsCreateOpen} />
		</div>
	);
}
