import { Hash, Tag } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { cn } from "src/core/utils/classes";
import type { CreateChannelModalProps } from "./use-create-modal";
import useCreateChannelModal from "./use-create-modal";

export default function CreateChannelModal({
	isOpen,
	setIsOpen,
}: CreateChannelModalProps) {
	const { channel } = useService();
	const { form, handleClose, isPending } = useCreateChannelModal({
		isOpen,
		setIsOpen,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Crear Canal</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(channel.validateName(value));
											return undefined;
										} catch (e) {
											return [{ message: "El nombre ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre del canal"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="state"
								children={({ FSSelectField }) => (
									<FSSelectField
										label="Estado"
										placeholder="Seleccionar estado"
										prefixComponent={<Hash size={16} />}
										options={[
											{ value: "active", label: "Activo" },
											{ value: "inactive", label: "Inactivo" },
										]}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
