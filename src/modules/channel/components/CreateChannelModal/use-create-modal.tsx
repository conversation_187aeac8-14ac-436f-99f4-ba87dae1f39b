import { toast } from "react-toastify";
import { useAppForm } from "src/core/components/form/form";
import { getErrorResult } from "src/core/utils/effectErrors";
import useCreateChannel from "../../hooks/use-create-channel";
import { CreateChannelSchema } from "../schema";

export interface CreateChannelModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	state: "",
} as CreateChannelSchema;

export default function useCreateChannelModal({
	setIsOpen,
}: CreateChannelModalProps) {
	const { mutate, isPending } = useCreateChannel();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateChannelSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onSuccess: () => {
					toast.success("Canal creado");
					handleClose();
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
