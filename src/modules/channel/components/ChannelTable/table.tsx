import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "src/core/components/tables/BasicTable";
import type { Channel } from "../../service/model/channel";
import { columns } from "./columns";

interface Props {
	channels: Channel[];
}

export default function Table({ channels }: Props) {
	const table = useReactTable({
		data: channels,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
