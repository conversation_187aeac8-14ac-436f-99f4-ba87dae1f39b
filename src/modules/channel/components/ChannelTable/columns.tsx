import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import type { Channel } from "../../service/model/channel";
import DeleteChannelModal from "../DeleteChannelModal";
import EditChannelModal from "../EditChannelModal";

const columnHelper = createColumnHelper<Channel>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("state", {
		header: "Estado",
		cell: (info) => {
			const state = info.getValue();
			return (
				<span
					className={`badge ${
						state === "active" ? "badge-success" : "badge-error"
					}`}
				>
					{state === "active" ? "Activo" : "Inactivo"}
				</span>
			);
		},
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const channel = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditChannelModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={channel.id}
					/>
					<DeleteChannelModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						channel={channel}
					/>
				</div>
			);
		},
	}),
];
