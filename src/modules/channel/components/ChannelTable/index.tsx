import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import { channelOptions } from "../../hooks/channel-options";
import Table from "./table";

export default function ChannelTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(channelOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table channels={data} />;
}
