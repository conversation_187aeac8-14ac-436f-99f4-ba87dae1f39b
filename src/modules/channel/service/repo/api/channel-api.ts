import { <PERSON>ttp<PERSON><PERSON> } from "@effect/platform";
import { Effect, Layer, Schema } from "effect";
import { ApiHttpClient } from "~/core/service/repo/api";
import { handleDResponse, handleResponse } from "~/core/service/repo/api/utils";
import type { CreateChannel, UpdateChannel } from "../../model/channel";
import { ChannelRepository } from "../../model/repository";
import {
	ChannelFromApi,
	ChannelListFromApi,
	CreateChannelApiFromCreateChannel,
	CreateChannelApiResponse,
	UpdateChannelApiFromUpdateChannel,
} from "./dto";

const baseUrl = "/v1/channels";

const makeChannelApiRepo = ApiHttpClient.pipe(
	Effect.andThen(({ httpClient }) => httpClient),
	Effect.andThen((httpClient) => ({
		getAll: () =>
			httpClient
				.get(baseUrl)
				.pipe(Effect.flatMap(handleDResponse(ChannelListFromApi))),
		getById: (id: string) =>
			httpClient
				.get(`${baseUrl}/${id}`)
				.pipe(Effect.flatMap(handleDResponse(ChannelFromApi))),
		create: (channel: CreateChannel) =>
			httpClient
				.post(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(CreateChannelApiFromCreateChannel)(
							channel,
						),
					),
				})
				.pipe(Effect.flatMap(handleDResponse(CreateChannelApiResponse))),
		update: (channel: UpdateChannel) =>
			httpClient
				.put(baseUrl, {
					body: HttpBody.unsafeJson(
						Schema.decodeUnknownSync(UpdateChannelApiFromUpdateChannel)(
							channel,
						),
					),
				})
				.pipe(Effect.flatMap(handleResponse)),
		delete: (id: string) =>
			httpClient.del(`${baseUrl}/${id}`).pipe(Effect.flatMap(handleResponse)),
		validateName: (name: string) =>
			httpClient
				.get(`${baseUrl}/validate/name/${name}`)
				.pipe(Effect.flatMap(handleResponse)),
	})),
	Effect.provide(ApiHttpClient.Live),
);

export const channelApiRepoLive = Layer.effect(
	ChannelRepository,
	makeChannelApiRepo,
);
