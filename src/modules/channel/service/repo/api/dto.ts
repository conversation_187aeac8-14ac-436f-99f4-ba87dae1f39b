import { Schema } from "effect";
import { Channel, CreateChannel, UpdateChannel } from "../../model/channel";

export const ChannelApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	state: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ChannelFromApi = Schema.transform(ChannelApi, Channel, {
	strict: true,
	decode: (channelApi) => ({
		...channelApi,
		createdAt: channelApi.created_at,
		updatedAt: channelApi.updated_at,
		deletedAt: channelApi.deleted_at,
	}),
	encode: (channel) => ({
		...channel,
		created_at: channel.createdAt,
		updated_at: channel.updatedAt,
		deleted_at: channel.deletedAt,
	}),
});

export const ChannelListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ChannelFromApi))),
	Schema.mutable(Schema.Array(Channel)),
	{
		strict: true,
		decode: (channelApiList) => (channelApiList ? channelApiList : []),
		encode: (channelList) => channelList,
	},
);

export const CreateChannelApi = Schema.Struct({
	name: Schema.String,
	state: Schema.String,
});

export const CreateChannelApiFromCreateChannel = Schema.transform(
	CreateChannel,
	CreateChannelApi,
	{
		strict: true,
		decode: (createChannel) => createChannel,
		encode: (createChannelApi) => createChannelApi,
	},
);

export const UpdateChannelApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	state: Schema.String,
});

export const UpdateChannelApiFromUpdateChannel = Schema.transform(
	UpdateChannel,
	UpdateChannelApi,
	{
		strict: true,
		decode: (updateChannel) => updateChannel,
		encode: (updateChannelApi) => updateChannelApi,
	},
);

export const CreateChannelApiResponse = Schema.String;
