import { Effect } from "effect";
import type { AppError } from "src/core/service/model/error";
import type { Channel, CreateChannel, UpdateChannel } from "./channel";

export class ChannelUsecase extends Effect.Tag("ChannelUsecase")<
	ChannelUsecase,
	{
		readonly getAll: () => Effect.Effect<Channel[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Channel, AppError>;
		readonly create: (channel: CreateChannel) => Effect.Effect<string, AppError>;
		readonly update: (channel: UpdateChannel) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateName: (name: string) => Effect.Effect<void, AppError>;
	}
>() {}
