import { Schema } from "effect";

export const Channel = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	state: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Channel = typeof Channel.Type;

export const CreateChannel = Schema.Struct({
	name: Schema.String,
	state: Schema.String,
});
export type CreateChannel = typeof CreateChannel.Type;

export const UpdateChannel = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	state: Schema.String,
});
export type UpdateChannel = typeof UpdateChannel.Type;
