import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Channel, UpdateChannel } from "../service/model/channel";
import { channelOptions } from "./channel-options";

export default function useUpdateChannel() {
	const service = useService();
	const { channel } = service;
	const queryClient = useQueryClient();
	const queryKey = channelOptions(service).queryKey;

	return useMutation({
		mutationFn: (updatedChannel: UpdateChannel) =>
			AppRuntime.runPromise(channel.update(updatedChannel)),
		onMutate: async (updatedChannel) => {
			await queryClient.cancelQueries({ queryKey });

			const previousData = queryClient.getQueryData<Channel[]>(queryKey);

			queryClient.setQueryData<Channel[]>(query<PERSON>ey, (old) =>
				create(old || [], (draft) => {
					const index = draft.findIndex((item) => item.id === updatedChannel.id);
					if (index !== -1) {
						draft[index] = {
							...draft[index],
							name: updatedChannel.name,
							state: updatedChannel.state,
							updatedAt: new Date().toISOString(),
						};
					}
				}),
			);

			return { previousData };
		},
		onError: (_error, _variables, context) => {
			if (context?.previousData) {
				queryClient.setQueryData(queryKey, context.previousData);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
