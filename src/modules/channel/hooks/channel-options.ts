import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const channelOptions = ({ channel }: serviceRegistry) =>
	queryOptions({
		queryKey: ["channels"],
		queryFn: () => AppRuntime.runPromise(channel.getAll()),
	});

export const channelOptionsById = ({ channel }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["channels", id],
		queryFn: () => AppRuntime.runPromise(channel.getById(id)),
	});
