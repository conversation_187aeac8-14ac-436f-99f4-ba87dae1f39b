import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Channel } from "../service/model/channel";
import { channelOptions } from "./channel-options";

export default function useDeleteChannel() {
	const service = useService();
	const { channel } = service;
	const queryClient = useQueryClient();
	const queryKey = channelOptions(service).queryKey;

	return useMutation({
		mutationFn: (id: string) => AppRuntime.runPromise(channel.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousData = queryClient.getQueryData<Channel[]>(queryKey);

			queryClient.setQueryData<Channel[]>(queryKey, (old) =>
				create(old || [], (draft) => {
					const index = draft.findIndex((item) => item.id === id);
					if (index !== -1) {
						draft.splice(index, 1);
					}
				}),
			);

			return { previousData };
		},
		onError: (_error, _variables, context) => {
			if (context?.previousData) {
				queryClient.setQueryData(queryKey, context.previousData);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
