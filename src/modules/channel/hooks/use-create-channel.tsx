import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { Channel, CreateChannel } from "../service/model/channel";
import { channelOptions } from "./channel-options";

export default function useCreateChannel() {
	const service = useService();
	const { channel } = service;
	const queryClient = useQueryClient();
	const queryKey = channelOptions(service).queryKey;

	return useMutation({
		mutationFn: (newChannel: CreateChannel) =>
			AppRuntime.runPromise(channel.create(newChannel)),
		onMutate: async (newChannel) => {
			await queryClient.cancelQueries({ queryKey });

			const previousData = queryClient.getQueryData<Channel[]>(queryKey);

			queryClient.setQueryData<Channel[]>(queryKey, (old) =>
				create(old || [], (draft) => {
					draft.push({
						id: "temp-id",
						name: newChannel.name,
						state: newChannel.state,
						createdAt: new Date().toISOString(),
						updatedAt: new Date().toISOString(),
						deletedAt: null,
					});
				}),
			);

			return { previousData };
		},
		onError: (_error, _variables, context) => {
			if (context?.previousData) {
				queryClient.setQueryData(queryKey, context.previousData);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
