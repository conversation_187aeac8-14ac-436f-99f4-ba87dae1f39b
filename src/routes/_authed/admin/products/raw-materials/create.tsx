import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import CreateRawMaterialForm from "~/raw-materials/components/CreateRawMaterialForm";

export const Route = createFileRoute(
	"/_authed/admin/products/raw-materials/create",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Crear Materias Primas",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="container mx-auto ">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/products/raw-materials"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Crear Materia Prima</h1>
				</div>
			</div>

			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información de la Materia Prima</h2>
						<CreateRawMaterialForm />
					</div>
				</div>
			</div>
		</div>
	);
}
