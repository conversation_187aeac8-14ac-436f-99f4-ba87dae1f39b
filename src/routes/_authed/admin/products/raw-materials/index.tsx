import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import RawMaterialsTable from "~/modules/raw-materials/components/RawMaterialsTable";

export const Route = createFileRoute("/_authed/admin/products/raw-materials/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Materias Primas",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<>
			<div className="container mx-auto ">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<Link
								to="/admin/products/raw-materials/create"
								className="btn btn-primary"
							>
								<Plus size={16} />
								Crear nueva materia prima
							</Link>
						</div>
					</div>
					<RawMaterialsTable />
				</div>
			</div>
		</>
	);
}
