import { useQuery } from "@tanstack/react-query";
import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import EditProductForm from "~/product/components/EditProductForm";
import EditProductTabs from "~/product/components/EditProductTabs";
import { productOptionsById } from "~/product/hooks/product-options";

export const Route = createFileRoute(
	"/_authed/admin/products/products/edit/$id",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Editar Productos",
			},
		],
	}),
});

function RouteComponent() {
	const { id } = Route.useParams();
	const service = useService();

	// Fetch product data
	const { data, isLoading, error } = useQuery(productOptionsById(service, id));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isLoading) {
		return (
			<div className="container mx-auto">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	if (!data) {
		return (
			<div className="container mx-auto">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link to="/admin/products/products" className="btn btn-ghost btn-sm">
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Editar Producto</h1>
				</div>
			</div>
			<EditProductTabs />
			<EditProductForm product={data} />
		</div>
	);
}
