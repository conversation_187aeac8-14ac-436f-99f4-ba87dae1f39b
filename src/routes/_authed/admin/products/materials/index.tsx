import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import MaterialsTable from "~/materials/components/MaterialsTable";

export const Route = createFileRoute("/_authed/admin/products/materials/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Materiales",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<>
			<div className="container mx-auto ">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<Link
								to="/admin/products/materials/create"
								className="btn btn-primary"
							>
								<Plus size={16} />
								Crear nuevo material
							</Link>
						</div>
					</div>
					<MaterialsTable />
				</div>
			</div>
		</>
	);
}
