import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import BrandTable from "src/modules/brand/components/BrandTable";
import CreateBrandModal from "src/modules/brand/components/CreateBrandModal";

export const Route = createFileRoute("/_authed/admin/products/brands/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Marcas",
			},
		],
	}),
});

function RouteComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto ">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsOpen(true)}
							>
								Crear nueva marca
							</button>
						</div>
					</div>
					<BrandTable />
				</div>
			</div>
			<CreateBrandModal isOpen={isOpen} setIsOpen={setIsOpen} />
		</>
	);
}
