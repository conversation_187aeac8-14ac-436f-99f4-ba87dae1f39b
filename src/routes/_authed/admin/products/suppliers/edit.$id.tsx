import { useQuery } from "@tanstack/react-query";
import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import { productOptionsById } from "src/modules/product/hooks/product-options";
import EditSupplierForm from "src/modules/suppliers/components/EditSupplierForm";

export const Route = createFileRoute(
	"/_authed/admin/products/suppliers/edit/$id",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Editar Insumos",
			},
		],
	}),
});

function RouteComponent() {
	const { id } = Route.useParams();
	const service = useService();

	// Fetch product data
	const { data, isLoading, error } = useQuery(productOptionsById(service, id));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isLoading) {
		return (
			<div className="container mx-auto ">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto ">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	if (!data) {
		return (
			<div className="container mx-auto ">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto ">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link to="/admin/products/suppliers" className="btn btn-ghost btn-sm">
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Editar Insumo</h1>
				</div>
			</div>

			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información del Insumo</h2>
						<EditSupplierForm product={data} />
					</div>
				</div>
			</div>
		</div>
	);
}
