import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import CreateProductionFlowForm from "src/modules/production-flow/components/CreateProductionFlowPage";
import type { ActivitySchema } from "src/modules/production-flow/components/schemas";

export const Route = createFileRoute(
	"/_authed/admin/manufacture/production-flow/create",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Crar Flujo de Producción",
			},
		],
	}),
});

function RouteComponent() {
	const [activities, setActivities] = useState<ActivitySchema[]>([]);

	return (
		<div className="container mx-auto ">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/manufacture/production-flow"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Crear Flujo de Producción</h1>
				</div>
			</div>

			<CreateProductionFlowForm
				activities={activities}
				setActivities={setActivities}
			/>
		</div>
	);
}
