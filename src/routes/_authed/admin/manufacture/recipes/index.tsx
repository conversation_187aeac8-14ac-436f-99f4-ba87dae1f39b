import { Link, createFileRoute } from "@tanstack/react-router";
import { Plus } from "lucide-react";
import RecipeTable from "~/modules/recipe/components/RecipeTable";

export const Route = createFileRoute("/_authed/admin/manufacture/recipes/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Recetas",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="container mx-auto space-y-6">
			<div className="flex items-center justify-between">
				<Link
					to="/admin/manufacture/recipes/create"
					className="btn btn-primary"
				>
					<Plus size={16} />
					Nueva Receta
				</Link>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<RecipeTable />
				</div>
			</div>
		</div>
	);
}
