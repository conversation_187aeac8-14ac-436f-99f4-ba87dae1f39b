import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft, Package, Scale } from "lucide-react";
import { useState } from "react";
import CreateRecipeForm from "src/modules/recipe/components/CreateRecipeForm";

export const Route = createFileRoute(
	"/_authed/admin/manufacture/recipes/create",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Crear Receta",
			},
		],
	}),
});

function RouteComponent() {
	const [selectedType, setSelectedType] = useState<"bulk" | "unit" | null>(
		null,
	);

	if (selectedType) {
		return (
			<div className="container mx-auto ">
				<div className="mb-6">
					<div className="mb-4 flex items-center gap-4">
						<button
							type="button"
							className="btn btn-ghost btn-sm"
							onClick={() => setSelectedType(null)}
						>
							<ArrowLeft size={16} />
							Volver
						</button>
						<h1 className="font-bold text-2xl">
							<PERSON><PERSON><PERSON> {selectedType === "bulk" ? "a Granel" : "por Unidad"}
						</h1>
					</div>
				</div>

				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<CreateRecipeForm recipeType={selectedType} />
					</div>
				</div>
			</div>
		);
	}
	return (
		<div className="container mx-auto ">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/manufacture/recipes"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Crear Receta</h1>
				</div>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title mb-8 text-center">
						Selecciona el tipo de receta que deseas crear
					</h2>

					<div className="grid grid-cols-1 gap-8 md:grid-cols-2">
						{/* Bulk Recipe Card */}
						<button
							type="button"
							className="card group cursor-pointer border border-primary/20 bg-gradient-to-br from-primary/10 to-primary/5 transition-all duration-300 hover:scale-105 hover:border-primary/40 hover:shadow-xl"
							onClick={() => setSelectedType("bulk")}
						>
							<div className="card-body items-center p-8 text-center">
								<div className="mb-4 rounded-full bg-primary/10 p-4 transition-colors duration-300 group-hover:bg-primary/20">
									<Scale
										size={48}
										className="text-primary transition-transform duration-300 group-hover:scale-110"
									/>
								</div>
								<h3 className="card-title mb-3 text-xl transition-colors duration-300 group-hover:text-primary">
									Receta a Granel
								</h3>
								<p className="mb-4 text-base-content/70 leading-relaxed">
									Ideal para productos que se fabrican en grandes cantidades y
									se miden por peso o volumen.
								</p>
								<div className="mb-4 rounded-lg bg-base-200 p-4">
									<h4 className="mb-2 font-semibold text-primary text-sm">
										Características:
									</h4>
									<ul className="space-y-1 text-base-content/80 text-sm">
										<li>• Medición por peso/volumen</li>
										<li>• Producción en lotes grandes</li>
										<li>• Ingredientes líquidos o en polvo</li>
										<li>• Escalable fácilmente</li>
									</ul>
								</div>
								<div className="rounded-lg bg-info/10 p-3">
									<p className="font-medium text-info text-sm">
										💡 Perfecto para salsas, masas, mezclas y productos líquidos
									</p>
								</div>
							</div>
						</button>

						{/* Unit Recipe Card */}
						<button
							type="button"
							className="card group cursor-pointer border border-secondary/20 bg-gradient-to-br from-secondary/10 to-secondary/5 transition-all duration-300 hover:scale-105 hover:border-secondary/40 hover:shadow-xl"
							onClick={() => setSelectedType("unit")}
						>
							<div className="card-body items-center p-8 text-center">
								<div className="mb-4 rounded-full bg-secondary/10 p-4 transition-colors duration-300 group-hover:bg-secondary/20">
									<Package
										size={48}
										className="text-secondary transition-transform duration-300 group-hover:scale-110"
									/>
								</div>
								<h3 className="card-title mb-3 text-xl transition-colors duration-300 group-hover:text-secondary">
									Receta por Unidad
								</h3>
								<p className="mb-4 text-base-content/70 leading-relaxed">
									Perfecta para productos que se fabrican individualmente con
									cantidades específicas por unidad.
								</p>
								<div className="mb-4 rounded-lg bg-base-200 p-4">
									<h4 className="mb-2 font-semibold text-secondary text-sm">
										Características:
									</h4>
									<ul className="space-y-1 text-base-content/80 text-sm">
										<li>• Medición por unidades exactas</li>
										<li>• Producción pieza por pieza</li>
										<li>• Ingredientes específicos</li>
										<li>• Control preciso de porciones</li>
									</ul>
								</div>
								<div className="rounded-lg bg-warning/10 p-3">
									<p className="font-medium text-sm text-warning">
										💡 Ideal para productos empaquetados, porciones individuales
									</p>
								</div>
							</div>
						</button>
					</div>

					<div className="mt-8 text-center">
						<p className="text-base-content/60 text-sm">
							Haz clic en cualquiera de las opciones para continuar con la
							creación de tu receta
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
