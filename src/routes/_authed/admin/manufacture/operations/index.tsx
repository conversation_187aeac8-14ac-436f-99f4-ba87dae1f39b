import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import CreateOperationModal from "src/modules/operation/components/CreateOperationModal";
import OperationTable from "src/modules/operation/components/OperationTable";

export const Route = createFileRoute("/_authed/admin/manufacture/operations/")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Operaciones",
			},
		],
	}),
});

function RouteComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto ">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsOpen(true)}
							>
								Crear nueva operación
							</button>
						</div>
					</div>
					<OperationTable />
				</div>
			</div>
			<CreateOperationModal isOpen={isOpen} setIsOpen={setIsOpen} />
		</>
	);
}
