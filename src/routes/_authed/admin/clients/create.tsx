import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import ClientForm from "src/modules/client/components/ClientForm";
import useCreateClient from "src/modules/client/hooks/use-create-client";
import type { CreateClient } from "src/modules/client/service/model/client";

export const Route = createFileRoute("/_authed/admin/clients/create")({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Crear Cliente",
			},
		],
	}),
});

function RouteComponent() {
	const navigate = useNavigate();
	const createClientMutation = useCreateClient();

	const handleSubmit = (data: CreateClient) => {
		createClientMutation.mutate(data, {
			onSuccess: () => {
				navigate({ to: "/admin/clients/clients" });
			},
		});
	};

	const handleBack = () => {
		navigate({ to: "/admin/clients/clients" });
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center gap-4">
				<button
					type="button"
					onClick={handleBack}
					className="btn btn-ghost btn-sm"
				>
					<ArrowLeft size={16} />
					Volver
				</button>
				<h1 className="text-2xl font-bold">Crear Cliente</h1>
			</div>
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<ClientForm
						onSubmit={handleSubmit}
						isLoading={createClientMutation.isPending}
						submitText="Crear Cliente"
					/>
				</div>
			</div>
		</div>
	);
}
