import React from "react";

export default function UnderConstructionThemed() {
	return (
		<div className="flex min-h-screen flex-col items-center justify-center bg-base-100 p-4 font-sans">
			{/* Título Principal usando colores de tema */}

			{/* Contenedor de la ilustración */}
			<div className="relative w-full max-w-2xl" style={{ height: "500px" }}>
				{/* --- Elementos Colgantes (usando accent y warning) --- */}
				<div className="absolute top-0 left-[15%] h-32 w-0.5 bg-base-300">
					<div className="-bottom-8 -left-4 absolute h-8 w-8 rotate-45 transform bg-accent" />
				</div>
				<div className="absolute top-0 left-[5%] h-48 w-0.5 bg-base-300">
					<div className="-bottom-5 -left-4 absolute flex h-9 w-9 rotate-45 transform items-center justify-center bg-warning font-bold text-2xl">
						<span className="-rotate-45 transform text-warning-content">!</span>
					</div>
				</div>

				{/* --- Monitor Central (usando neutral y base) --- */}
				<div className="-translate-x-1/2 absolute bottom-16 left-1/2">
					<div className="relative h-56 w-80 rounded-t-lg border-8 border-neutral bg-base-200">
						{/* Contenido de la pantalla (Wireframe) */}
						<div className="h-full w-full bg-base-100 p-3">
							<div className="mb-2 h-5 w-full rounded bg-base-300" />
							<div className="flex items-center space-x-2">
								<div className="flex-1 space-y-1.5">
									<div className="h-2 w-full rounded bg-base-300" />
									<div className="h-2 w-5/6 rounded bg-base-300" />
								</div>
								<div className="flex h-10 w-10 items-center justify-center rounded bg-base-300">
									{/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
									<svg
										width="20"
										height="20"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										className="text-base-content/50"
									>
										<rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
										<circle cx="9" cy="9" r="2" />
										<path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
									</svg>
								</div>
							</div>
						</div>
					</div>
					{/* Base del monitor (usando secondary y neutral) */}
					<div className="relative mx-auto flex h-10 w-20 items-end justify-center bg-secondary">
						<div className="h-3 w-full bg-neutral" />
					</div>
					<div className="mx-auto h-1.5 w-32 rounded bg-neutral" />
				</div>

				{/* --- Grúa Principal (usando primary, secondary, y error) --- */}
				<div className="absolute right-10 bottom-0 h-[80%] w-16">
					<div className="absolute bottom-0 left-0 h-full w-full bg-secondary" />
					<div className="-left-32 absolute top-12 h-6 w-48 bg-primary" />
					<div className="-translate-x-1/2 absolute top-0 left-1/2 h-12 w-20 border-primary-focus border-b-4 bg-primary">
						<div className="absolute top-1 right-1 h-2.5 w-2.5 animate-pulse rounded-full bg-error" />
					</div>
					<div className="absolute top-12 right-0 h-8 w-6 bg-neutral" />

					{/* Cable y Panel con Texto (usando warning) */}
					<div className="absolute top-16 left-[-105px] flex flex-col items-center">
						<div className="h-12 w-px bg-neutral-content" />
						<div className="-rotate-6 flex h-20 w-40 items-center justify-center rounded-md border-4 border-warning bg-warning/80 p-2 text-center backdrop-blur-sm">
							<span className="font-bold text-lg text-warning-content drop-shadow-md">
								En costrucción
							</span>
						</div>
					</div>
				</div>

				{/* --- Elementos del Suelo (usando warning y primary) --- */}
				<div className="absolute bottom-12 left-[28%] h-10 w-8">
					<div className="h-1/3 w-full bg-warning" />
					<div className="h-1/3 w-full bg-base-100" />
					<div className="h-1/3 w-full bg-warning" />
				</div>
				<div className="absolute right-[28%] bottom-12 h-6 w-16 rotate-12">
					<div className="h-6 w-6 rounded-sm bg-neutral" />
					<div className="-translate-y-1/2 absolute top-1/2 left-5 h-2.5 w-12 rounded-sm bg-primary" />
				</div>
			</div>
			<h1 className="mb-4 font-bold text-4xl text-base-content">
				Sitio en Construcción
			</h1>
			<p className="mb-8 text-base-content/80">En desarrollo</p>
		</div>
	);
}
