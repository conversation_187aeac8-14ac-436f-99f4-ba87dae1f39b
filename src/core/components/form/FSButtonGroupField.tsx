import { useFieldContext } from "./form";

type Option = {
	value: string;
	label: string;
	icon?: React.ReactNode;
	description?: string;
};

interface Props {
	label: string;
	options: Option[];
	details?: string;
}

export function FSButtonGroupField({ label, options, details }: Props) {
	const field = useFieldContext<string>();

	const isTouched = field.state.meta.isTouched;
	const errorsLength = field.state.meta.errors.length;
	const isError = isTouched && errorsLength;
	const errors = field.state.meta.errors;

	return (
		<fieldset className="fieldset">
			<legend className="fieldset-legend">{label}</legend>
			{details && (
				<p className="fieldset-label mb-3 text-base-content/70 text-sm">
					{details}
				</p>
			)}
			<div className="flex gap-3">
				{options.map((option) => {
					const isSelected = field.state.value === option.value;
					return (
						<button
							key={option.value}
							type="button"
							className={`btn h-fit flex-1 ${
								isSelected
									? "btn-primary"
									: "btn-outline hover:btn-primary hover:border-primary"
							}`}
							onClick={() => field.handleChange(option.value)}
						>
							<div className="flex-col gap-1 px-2">
								{option.icon && (
									<div className="flex justify-center">{option.icon}</div>
								)}
								<p className="font-medium">{option.label}</p>
								{option.description && (
									<span className="text-xs opacity-70">
										{option.description}
									</span>
								)}
							</div>
						</button>
					);
				})}
			</div>
			{isError
				? errors.flatMap(({ message }) => (
						<p key={message} className="fieldset-label text-error">
							{message}
						</p>
					))
				: null}
		</fieldset>
	);
}
