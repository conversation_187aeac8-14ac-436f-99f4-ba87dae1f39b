import { useFieldContext } from "./form";

type Option = { value: number; label: string };

interface Props {
	label: string;
	options: Option[];
}

export function FSCheckboxGroupField({ label, options }: Props) {
	const field = useFieldContext<number[]>();

	const isTouched = field.state.meta.isTouched;
	const errorsLength = field.state.meta.errors.length;
	const isError = isTouched && errorsLength;
	const errors = field.state.meta.errors;

	const handleCheckboxChange = (value: number, checked: boolean) => {
		const currentValues = field.state.value || [];
		if (checked) {
			// Add value if not already present
			if (!currentValues.includes(value)) {
				field.handleChange([...currentValues, value].sort());
			}
		} else {
			// Remove value
			field.handleChange(currentValues.filter((v) => v !== value));
		}
	};

	return (
		<fieldset className="fieldset">
			<legend className="fieldset-legend">{label}</legend>
			<div className="grid grid-cols-2 gap-2 md:grid-cols-4">
				{options.map((option) => (
					<label key={option.value} className="label cursor-pointer">
						<input
							type="checkbox"
							className="checkbox checkbox-sm"
							checked={field.state.value?.includes(option.value) || false}
							onChange={(e) =>
								handleCheckboxChange(option.value, e.target.checked)
							}
						/>
						<span className="label-text ml-2">{option.label}</span>
					</label>
				))}
			</div>
			{isError
				? errors.flatMap(({ message }) => (
						<p key={message} className="fieldset-label text-error">
							{message}
						</p>
					))
				: null}
		</fieldset>
	);
}
