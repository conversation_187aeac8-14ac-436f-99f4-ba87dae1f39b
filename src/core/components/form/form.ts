import { createF<PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON><PERSON>ontexts } from "@tanstack/react-form";
import { FSButtonGroupField } from "./FSButtonGroupField";
import { FSCheckboxGroupField } from "./FSCheckboxGroupField";
import FSComboB<PERSON>Field from "./FSComboBoxField";
import { FSPasswordField } from "./FSPasswordField";
import { FSSelectField } from "./FSSelectField";
import { FSTextAreaField } from "./FSTextArea";
import { FSTextField } from "./FSTextField";
import { FSToggleField } from "./FSToggleField";
import { SubscribeButton } from "./SubscribeButton";

export const { fieldContext, useFieldContext, formContext, useFormContext } =
	createFormHookContexts();

export const { useAppForm, withForm } = createFormHook({
	fieldComponents: {
		FSTextField,
		FSPasswordField,
		FSSelectField,
		FSToggleField,
		FSComboBoxField,
		FSTextAreaField,
		FSCheckboxGroupField,
		FSButtonGroupField,
	},
	formComponents: {
		SubscribeButton,
	},
	fieldContext,
	formContext,
});
